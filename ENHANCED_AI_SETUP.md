# Enhanced AI Agent Setup Guide
## DeepSeek-R1 Model + Google Custom Search Integration

This guide explains how to set up the enhanced AI Agent with DeepSeek-R1 local LLM and Google Custom Search capabilities.

## 🧠 **DeepSeek-R1 Model Integration**

### **Model Configuration**
- **Model**: `deepseek-ai/DeepSeek-R1-0528`
- **Implementation**: Transformers library with pipeline approach
- **Fallback**: Graceful degradation to Hugging Face API and intelligent responses

### **System Requirements**
- **GPU**: NVIDIA GPU with CUDA support (recommended for optimal performance)
- **RAM**: Minimum 16GB, 32GB recommended
- **Storage**: 20GB+ free space for model files
- **Python**: 3.8+ with transformers library

### **Installation Steps**

1. **Install Dependencies**:
```bash
pip install transformers>=4.36.0
pip install torch>=2.0.0
pip install accelerate>=0.20.0
```

2. **Configure Environment**:
```bash
# In .env file
ENABLE_DEEPSEEK_MODEL=True
HUGGINGFACE_TOKEN=your_token_here  # Optional, for better model access
```

3. **GPU Setup** (Optional but recommended):
- Install CUDA toolkit
- Verify GPU availability: `torch.cuda.is_available()`

### **Model Loading Behavior**
1. **Primary**: Pipeline approach with `trust_remote_code=True`
2. **Fallback**: Direct model loading with AutoTokenizer/AutoModelForCausalLM
3. **Final Fallback**: Intelligent response system if model fails

## 🌐 **Google Custom Search Integration**

### **API Configuration**
- **API Key**: `AIzaSyDLS7KjnBml_w1PJEMvtnrLyNwhQXJQpc0` (provided)
- **Search Engine**: Google Custom Search API v1
- **Fallback**: DuckDuckGo Instant Answers + Built-in Knowledge Base

### **Setup Steps**

1. **Environment Configuration**:
```bash
# In .env file
GOOGLE_API_KEY=AIzaSyDLS7KjnBml_w1PJEMvtnrLyNwhQXJQpc0
GOOGLE_CSE_ID=your_custom_search_engine_id  # Optional, has default
ENABLE_WEB_SEARCH=True
```

2. **Custom Search Engine** (Optional):
- Visit: https://cse.google.com/cse/
- Create a new search engine
- Get the CSE ID and add to .env

### **Search Capabilities**
- **Primary**: Google Custom Search with rich results
- **Secondary**: DuckDuckGo Instant Answers
- **Tertiary**: Built-in knowledge base for common terms
- **Caching**: 1-hour cache for search results

## 🔧 **Complete Configuration**

### **Environment Variables (.env)**
```bash
# Enhanced AI Agent Configuration with DeepSeek-R1 and Google Search

# Google Custom Search API
GOOGLE_API_KEY=AIzaSyDLS7KjnBml_w1PJEMvtnrLyNwhQXJQpc0
GOOGLE_CSE_ID=your_custom_search_engine_id_here

# DeepSeek-R1 Model
ENABLE_DEEPSEEK_MODEL=True
HUGGINGFACE_TOKEN=your_hf_token_here

# AI Agent Features
ENABLE_WEB_SEARCH=True
ENABLE_MEMORY_SYSTEM=True
ENABLE_LLM_FALLBACK=True

# Django Configuration
DEBUG=True
SECRET_KEY=your-secret-key-here
```

### **Testing the Setup**
```bash
# Run the comprehensive test
python test_deepseek_enhanced_agent.py

# Check agent status
python -c "
from analyzer.intelligent_agent import IntelligentAgent
from django.contrib.auth.models import User
import django, os
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'jira_analyzer.settings')
django.setup()
user = User.objects.first()
agent = IntelligentAgent(user)
print(agent.get_memory_status())
"
```

## 🚀 **Enhanced Capabilities**

### **1. DeepSeek-R1 Local LLM**
- **Advanced Reasoning**: Superior language understanding and generation
- **Context Awareness**: Maintains conversation context and database state
- **Local Processing**: No external API dependencies for core LLM functionality
- **Conversation Format**: Supports `[{"role": "user", "content": "message"}]` format

### **2. Google Custom Search**
- **Real-time Information**: Access to current web information
- **Rich Results**: Titles, snippets, and links from search results
- **Intelligent Caching**: Reduces API calls and improves response time
- **Fallback System**: Multiple search sources for reliability

### **3. Hybrid Intelligence**
- **Multi-source Analysis**: Combines Jira data + web search + knowledge base
- **Context Integration**: Seamlessly blends different information sources
- **Professional Responses**: Structured, detailed insights with citations
- **Memory System**: Persistent conversation and database context

## 🔍 **Usage Examples**

### **Jira Analysis with DeepSeek-R1**
```
User: "Analyze my latest Jira data and provide strategic recommendations"
AI: "🧠 DeepSeek-R1 Analysis: Based on your 66 tickets with 51.5% negative sentiment..."
```

### **Web Search Integration**
```
User: "What are the latest DevOps trends for 2024?"
AI: "🔍 Search Results: [Google Custom Search results with current information]"
```

### **Hybrid Intelligence**
```
User: "How can I apply modern agile practices to my current Jira workflow?"
AI: "📊 From Your Jira Data: [specific insights] 🌐 Additional Information: [web search results]"
```

## 🛠 **Troubleshooting**

### **DeepSeek-R1 Issues**
- **GPU Error**: Model requires GPU for optimal performance, falls back to CPU or intelligent responses
- **Memory Error**: Reduce model precision or use smaller batch sizes
- **Loading Error**: Check internet connection and Hugging Face access

### **Google Search Issues**
- **API Quota**: Monitor API usage and implement rate limiting if needed
- **Invalid Results**: Falls back to DuckDuckGo and knowledge base
- **Network Error**: Graceful degradation to offline knowledge

### **General Issues**
- **Slow Responses**: Check system resources and network connectivity
- **Memory Leaks**: Monitor conversation memory and implement cleanup
- **Fallback Behavior**: Verify all fallback systems are working correctly

## 📊 **Performance Monitoring**

### **Agent Status Check**
```python
memory_status = agent.get_memory_status()
# Returns:
{
    'deepseek_model': 'Ready (pipeline)' | 'Failed to Load',
    'google_search': True | False,
    'active_conversations': 1,
    'web_cache_size': 5,
    'database_memory': True
}
```

### **Expected Performance**
- **DeepSeek-R1**: 2-10 seconds per response (GPU), 10-30 seconds (CPU)
- **Google Search**: 1-3 seconds per query
- **Fallback Responses**: <1 second
- **Memory Usage**: 2-8GB RAM depending on model loading

## ✅ **Success Indicators**

1. **DeepSeek Model**: Status shows "Ready (pipeline)" or "Ready (direct)"
2. **Google Search**: API key configured and returning results
3. **Memory System**: Conversations persist across queries
4. **Hybrid Responses**: Combines Jira data with web search results
5. **Graceful Fallbacks**: System continues working even if components fail

The enhanced AI Agent now provides professional-grade intelligence with local LLM processing and real-time web access!
