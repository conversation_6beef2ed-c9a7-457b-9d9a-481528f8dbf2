import os
import requests
import logging
import re
from datetime import datetime, timedelta
from dotenv import load_dotenv
from analyzer.deepseek_llm_handler import DeepSeekLLMHandler
from analyzer.database_integration import DatabaseIntegrator

# Set up logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class IntelligentAgent:
    """
    Enhanced intelligent agent with proactive client management capabilities
    """
    
    def __init__(self, user):
        self.user = user
        self.hf_token = os.getenv('HUGGINGFACE_TOKEN', None)

        # Memory system
        self.conversation_memory = {}
        self.database_memory = {}
        self.web_search_cache = {}

        # Enhanced LLM configuration with DeepSeek-R1
        self.deepseek_handler = None
        self.enable_deepseek = os.getenv('ENABLE_DEEPSEEK_MODEL', 'True').lower() == 'true'

        # Initialize DeepSeek-R1 model
        if self.enable_deepseek:
            try:
                self.deepseek_handler = DeepSeekLLMHandler()
                logger.info("✅ DeepSeek-R1 handler initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize DeepSeek-R1: {str(e)}")
                self.deepseek_handler = None

        # Fallback LLM models (Hugging Face API)
        self.llm_models = [
            {
                "name": "microsoft/DialoGPT-large",
                "api_url": "https://api-inference.huggingface.co/models/microsoft/DialoGPT-large",
                "type": "conversational"
            },
            {
                "name": "google/flan-t5-large",
                "api_url": "https://api-inference.huggingface.co/models/google/flan-t5-large",
                "type": "text-generation"
            }
        ]

        self.current_model = None
        
        # System prompt for intelligent responses
        self.system_prompt = """You are an advanced AI data analyst assistant with comprehensive access to Jira analysis data and web search capabilities. 
        You can:
        1. Analyze Jira ticket data and provide specific insights:
           - Identify trends and patterns in client issues
           - Calculate key metrics and KPIs
           - Compare data across different time periods
           - Generate statistical analysis and visualizations
           - Provide actionable recommendations based on data
        
        2. Perform advanced data analysis:
           - Time series analysis for trend identification
           - Sentiment analysis of ticket descriptions
           - Priority and severity distribution analysis
           - Client-specific metrics and comparisons
           - Team performance and workload analysis
        
        3. Search the web for additional information when needed
        
        4. Remember previous conversations and context:
           - Maintain conversation history
           - Track analysis requests and results
           - Build on previous insights
        
        5. Provide intelligent, data-driven recommendations:
           - Suggest process improvements
           - Identify potential bottlenecks
           - Recommend resource allocation
           - Highlight areas needing attention
        
        Always be helpful, accurate, and provide specific insights with data-backed evidence.
        When you don't have information in the database, use web search to find relevant answers.
        Format your responses with clear sections, bullet points, and data visualizations when appropriate.
        """

        # Add data analysis capabilities
        self.analysis_capabilities = {
            'trend_analysis': True,
            'sentiment_analysis': True,
            'priority_analysis': True,
            'client_metrics': True,
            'team_metrics': True,
            'time_series': True,
            'comparative_analysis': True,
            'predictive_insights': True
        }

        self.llm_handler = DeepSeekLLMHandler()
        self.db_integrator = DatabaseIntegrator(user)
        self.last_analysis_time = None
        self.analysis_cache = {}
        self.risk_assessment_cache = {}

    async def process_message(self, message, conversation_id=None, context_data=None):
        """
        Process incoming message with enhanced context awareness
        """
        try:
            # Update conversation memory
            if conversation_id not in self.conversation_memory:
                self.conversation_memory[conversation_id] = []
            self.conversation_memory[conversation_id].append({
                'message': message,
                'timestamp': datetime.now(),
                'context': context_data
            })

            # Gather enhanced context
            enhanced_context = await self._gather_enhanced_context(context_data)

            # Check for critical client situations
            critical_clients = self.llm_handler.identify_critical_clients(enhanced_context)
            if critical_clients:
                enhanced_context['critical_clients'] = critical_clients

            # Generate response with enhanced context
            response = await self.llm_handler.generate_response(
                message,
                conversation_id,
                enhanced_context
            )

            # Add proactive recommendations if critical clients found
            if critical_clients:
                response += self._generate_proactive_recommendations(critical_clients)

            return response

        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return "I apologize, but I encountered an error processing your request. Please try again."

    async def _gather_enhanced_context(self, context_data):
        """
        Gather comprehensive context data for analysis
        """
        enhanced_context = context_data or {}

        try:
            # Get latest analysis if not in cache
            if not self.last_analysis_time or (datetime.now() - self.last_analysis_time) > timedelta(minutes=5):
                latest_analysis = await self.db_integrator.get_latest_analysis()
                if latest_analysis:
                    enhanced_context['latest_analysis'] = latest_analysis
                    self.analysis_cache = latest_analysis
                    self.last_analysis_time = datetime.now()
            else:
                enhanced_context['latest_analysis'] = self.analysis_cache

            # Get client metrics
            client_metrics = await self._get_client_metrics()
            if client_metrics:
                enhanced_context['client_metrics'] = client_metrics

            # Get trend analysis
            trends = await self._analyze_trends()
            if trends:
                enhanced_context['trends'] = trends

            # Get risk indicators
            risk_indicators = await self._assess_risks()
            if risk_indicators:
                enhanced_context['risk_indicators'] = risk_indicators

            return enhanced_context

        except Exception as e:
            logger.error(f"Error gathering enhanced context: {str(e)}")
            return enhanced_context

    async def _get_client_metrics(self):
        """
        Get comprehensive client metrics
        """
        try:
            # Get all tickets from the last 30 days
            recent_tickets = await self.db_integrator.get_tickets(
                days=30,
                include_metadata=True
            )

            client_metrics = {}
            for ticket in recent_tickets:
                client = ticket.get('client')
                if not client:
                    continue

                if client not in client_metrics:
                    client_metrics[client] = {
                        'ticket_count': 0,
                        'sentiment': 0,
                        'experience_score': 0,
                        'priority_distribution': {},
                        'ticket_volume_increase': 0
                    }

                metrics = client_metrics[client]
                metrics['ticket_count'] += 1

                # Update sentiment
                sentiment = ticket.get('sentiment', 0)
                metrics['sentiment'] = (metrics['sentiment'] * (metrics['ticket_count'] - 1) + sentiment) / metrics['ticket_count']

                # Update priority distribution
                priority = ticket.get('priority', 'Medium')
                metrics['priority_distribution'][priority] = metrics['priority_distribution'].get(priority, 0) + 1

                # Update experience score
                experience_score = ticket.get('experience_score', 0)
                metrics['experience_score'] = (metrics['experience_score'] * (metrics['ticket_count'] - 1) + experience_score) / metrics['ticket_count']

            # Calculate ticket volume increase
            for client, metrics in client_metrics.items():
                previous_tickets = await self.db_integrator.get_tickets(
                    days=60,
                    client=client,
                    end_date=datetime.now() - timedelta(days=30)
                )
                previous_count = len(previous_tickets)
                if previous_count > 0:
                    metrics['ticket_volume_increase'] = (metrics['ticket_count'] - previous_count) / previous_count

            return client_metrics

        except Exception as e:
            logger.error(f"Error getting client metrics: {str(e)}")
            return None

    async def _analyze_trends(self):
        """
        Analyze trends in ticket data
        """
        try:
            trends = {
                'ticket_volume': {},
                'priority_distribution': {},
                'sentiment_trends': {},
                'resolution_time': {}
            }

            # Get tickets for trend analysis
            tickets = await self.db_integrator.get_tickets(
                days=90,
                include_metadata=True
            )

            # Analyze ticket volume trends
            for ticket in tickets:
                date = ticket.get('created_date').date()
                trends['ticket_volume'][date] = trends['ticket_volume'].get(date, 0) + 1

            # Analyze priority distribution trends
            for ticket in tickets:
                date = ticket.get('created_date').date()
                priority = ticket.get('priority', 'Medium')
                if date not in trends['priority_distribution']:
                    trends['priority_distribution'][date] = {}
                trends['priority_distribution'][date][priority] = trends['priority_distribution'][date].get(priority, 0) + 1

            # Analyze sentiment trends
            for ticket in tickets:
                date = ticket.get('created_date').date()
                sentiment = ticket.get('sentiment', 0)
                if date not in trends['sentiment_trends']:
                    trends['sentiment_trends'][date] = {'sum': 0, 'count': 0}
                trends['sentiment_trends'][date]['sum'] += sentiment
                trends['sentiment_trends'][date]['count'] += 1

            # Calculate average sentiment per day
            for date in trends['sentiment_trends']:
                data = trends['sentiment_trends'][date]
                if data['count'] > 0:
                    trends['sentiment_trends'][date] = data['sum'] / data['count']

            # Analyze resolution time trends
            for ticket in tickets:
                if ticket.get('resolution_date'):
                    date = ticket.get('created_date').date()
                    resolution_time = (ticket['resolution_date'] - ticket['created_date']).total_seconds() / 3600  # hours
                    if date not in trends['resolution_time']:
                        trends['resolution_time'][date] = {'sum': 0, 'count': 0}
                    trends['resolution_time'][date]['sum'] += resolution_time
                    trends['resolution_time'][date]['count'] += 1

            # Calculate average resolution time per day
            for date in trends['resolution_time']:
                data = trends['resolution_time'][date]
                if data['count'] > 0:
                    trends['resolution_time'][date] = data['sum'] / data['count']

            return trends

        except Exception as e:
            logger.error(f"Error analyzing trends: {str(e)}")
            return None

    async def _assess_risks(self):
        """
        Assess risks based on ticket data
        """
        try:
            risk_indicators = {
                'high_priority_tickets': 0,
                'negative_sentiment_ratio': 0,
                'increasing_volume': False,
                'slow_resolution': False
            }

            # Get recent tickets
            recent_tickets = await self.db_integrator.get_tickets(
                days=30,
                include_metadata=True
            )

            if not recent_tickets:
                return risk_indicators

            # Count high priority tickets
            high_priority_count = sum(1 for t in recent_tickets if t.get('priority') in ['Critical', 'High'])
            risk_indicators['high_priority_tickets'] = high_priority_count

            # Calculate negative sentiment ratio
            negative_sentiment_count = sum(1 for t in recent_tickets if t.get('sentiment', 0) < 0)
            risk_indicators['negative_sentiment_ratio'] = negative_sentiment_count / len(recent_tickets)

            # Check for increasing volume
            previous_tickets = await self.db_integrator.get_tickets(
                days=60,
                end_date=datetime.now() - timedelta(days=30)
            )
            if previous_tickets:
                volume_increase = (len(recent_tickets) - len(previous_tickets)) / len(previous_tickets)
                risk_indicators['increasing_volume'] = volume_increase > 0.2  # 20% increase threshold

            # Check for slow resolution
            resolved_tickets = [t for t in recent_tickets if t.get('resolution_date')]
            if resolved_tickets:
                avg_resolution_time = sum(
                    (t['resolution_date'] - t['created_date']).total_seconds() / 3600
                    for t in resolved_tickets
                ) / len(resolved_tickets)
                risk_indicators['slow_resolution'] = avg_resolution_time > 48  # 48 hours threshold

            return risk_indicators

        except Exception as e:
            logger.error(f"Error assessing risks: {str(e)}")
            return None

    def _generate_proactive_recommendations(self, critical_clients):
        """
        Generate proactive recommendations for critical clients
        """
        recommendations = "\n\nProactive Recommendations:\n"
        
        for client in critical_clients:
            recommendations += f"\nFor {client['client_name']}:\n"
            recommendations += "Risk Factors:\n"
            for factor in client['risk_factors']:
                recommendations += f"- {factor}\n"

            # Generate email template based on risk score
            if client['risk_score'] >= 3:
                email = self.llm_handler.generate_proactive_email(client, 'escalation')
            elif client['risk_score'] == 2:
                email = self.llm_handler.generate_proactive_email(client, 'apologetic')
            else:
                email = self.llm_handler.generate_proactive_email(client, 'solution_focused')

            if email:
                recommendations += f"\nRecommended Communication:\n"
                recommendations += f"Subject: {email['subject']}\n"
                recommendations += f"Content: {email['content']}\n"

            recommendations += "\nAction Items:\n"
            for factor in client['risk_factors']:
                if "sentiment" in factor.lower():
                    recommendations += "- Schedule a client satisfaction review\n"
                if "volume" in factor.lower():
                    recommendations += "- Implement additional support resources\n"
                if "priority" in factor.lower():
                    recommendations += "- Establish priority support channel\n"
                if "experience" in factor.lower():
                    recommendations += "- Conduct service quality assessment\n"

        return recommendations

    def get_conversation_history(self, conversation_id):
        """
        Get conversation history for a specific conversation
        """
        return self.conversation_memory.get(conversation_id, [])

    def clear_conversation_history(self, conversation_id):
        """
        Clear conversation history for a specific conversation
        """
        if conversation_id in self.conversation_memory:
            del self.conversation_memory[conversation_id]

    async def process_intelligent_query(self, user_message, conversation_id=None, context_data=None):
        """
        Process user queries with enhanced intelligence, memory, and web access
        """
        try:
            # Update memory with current context
            await self._update_memory(conversation_id, context_data)
            
            # Analyze query intent
            query_intent = self._analyze_query_intent(user_message)
            
            # Generate response based on intent
            if query_intent['type'] == 'jira_analysis':
                response = await self._handle_jira_query(user_message, context_data, query_intent)
            elif query_intent['type'] == 'web_search':
                response = await self._handle_web_search_query(user_message, query_intent)
            elif query_intent['type'] == 'general_conversation':
                response = await self._handle_general_query(user_message, conversation_id)
            else:
                response = await self._handle_hybrid_query(user_message, context_data, query_intent)
            
            # Store response in memory
            await self._store_in_memory(conversation_id, user_message, response)
            
            return response
            
        except Exception as e:
            logger.error(f"Error in intelligent query processing: {str(e)}")
            return "I encountered an error while processing your request. Please try again or rephrase your question."

    def _analyze_query_intent(self, message):
        """
        Analyze user message to determine intent and required capabilities
        """
        message_lower = message.lower()
        
        # Jira analysis keywords
        jira_keywords = ['jira', 'ticket', 'issue', 'client', 'priority', 'sentiment', 'analysis', 'data', 'trend']
        
        # Web search keywords
        web_keywords = ['what is', 'how to', 'explain', 'define', 'search', 'find', 'latest news', 'current']
        
        # General conversation keywords
        general_keywords = ['hello', 'hi', 'help', 'thank', 'bye']
        
        jira_score = sum(1 for keyword in jira_keywords if keyword in message_lower)
        web_score = sum(1 for keyword in web_keywords if keyword in message_lower)
        general_score = sum(1 for keyword in general_keywords if keyword in message_lower)
        
        # Determine primary intent
        if jira_score > web_score and jira_score > general_score:
            intent_type = 'jira_analysis'
        elif web_score > jira_score and web_score > general_score:
            intent_type = 'web_search'
        elif general_score > 0:
            intent_type = 'general_conversation'
        else:
            intent_type = 'hybrid'
        
        return {
            'type': intent_type,
            'jira_score': jira_score,
            'web_score': web_score,
            'general_score': general_score,
            'keywords': {
                'jira': [kw for kw in jira_keywords if kw in message_lower],
                'web': [kw for kw in web_keywords if kw in message_lower],
                'general': [kw for kw in general_keywords if kw in message_lower]
            }
        }

    async def _handle_jira_query(self, message, context_data, query_intent):
        """
        Handle Jira-specific analysis queries with enhanced data analysis capabilities
        """
        try:
            # Prepare analysis context
            analysis_context = self._prepare_analysis_context(context_data)
            
            # Generate comprehensive analysis prompt
            analysis_prompt = self._prepare_analysis_prompt(message, analysis_context)
            
            # Get response from LLM
            response = await self._get_llm_response(analysis_prompt)
            
            # Enhance response with data visualizations if applicable
            enhanced_response = await self._enhance_with_visualizations(response, analysis_context)
            
            return enhanced_response
            
        except Exception as e:
            logger.error(f"Error in Jira query handling: {str(e)}")
            return "I encountered an error while analyzing the Jira data. Please try again."

    def _prepare_analysis_context(self, context_data):
        """
        Prepare comprehensive analysis context from available data
        """
        analysis_context = {
            'current_data': {},
            'historical_data': {},
            'comparative_metrics': {},
            'trend_indicators': {},
            'client_insights': {},
            'team_metrics': {}
        }
        
        if context_data:
            # Add latest analysis data
            if context_data.get('latest_analysis'):
                analysis_context['current_data'] = context_data['latest_analysis']
            
            # Add historical data
            if context_data.get('all_analyses'):
                analysis_context['historical_data'] = context_data['all_analyses']
            
            # Add comparative metrics
            if context_data.get('comparative_data'):
                analysis_context['comparative_metrics'] = context_data['comparative_data']
            
            # Add trend indicators
            if context_data.get('trends'):
                analysis_context['trend_indicators'] = context_data['trends']
            
            # Add client insights
            if context_data.get('client_metrics'):
                analysis_context['client_insights'] = context_data['client_metrics']
            
            # Add team metrics
            if context_data.get('team_metrics'):
                analysis_context['team_metrics'] = context_data['team_metrics']
        
        return analysis_context

    def _prepare_analysis_prompt(self, message, analysis_context):
        """
        Prepare a detailed analysis prompt with context
        """
        prompt = f"{self.system_prompt}\n\n"
        
        # Add current data context
        if analysis_context['current_data']:
            prompt += "Current Analysis Context:\n"
            for key, value in analysis_context['current_data'].items():
                prompt += f"- {key}: {value}\n"
        
        # Add historical context
        if analysis_context['historical_data']:
            prompt += "\nHistorical Context:\n"
            for analysis in analysis_context['historical_data'][-3:]:  # Last 3 analyses
                prompt += f"- {analysis.get('date')}: {analysis.get('summary')}\n"
        
        # Add comparative metrics
        if analysis_context['comparative_metrics']:
            prompt += "\nComparative Metrics:\n"
            for metric, value in analysis_context['comparative_metrics'].items():
                prompt += f"- {metric}: {value}\n"
        
        # Add trend indicators
        if analysis_context['trend_indicators']:
            prompt += "\nTrend Indicators:\n"
            for trend, data in analysis_context['trend_indicators'].items():
                prompt += f"- {trend}: {data}\n"
        
        prompt += f"\nUser Query: {message}\n"
        prompt += "Please provide a comprehensive analysis with specific insights, trends, and recommendations.\n"
        prompt += "Assistant Response:"
        
        return prompt

    async def _enhance_with_visualizations(self, response, analysis_context):
        """
        Enhance the response with data visualizations where appropriate
        """
        # Add visualization placeholders for key metrics
        if analysis_context['current_data']:
            response += "\n\n📊 **Key Metrics Visualization**:\n"
            response += "```\n"
            response += self._generate_ascii_chart(analysis_context['current_data'])
            response += "\n```\n"
        
        # Add trend visualization if available
        if analysis_context['trend_indicators']:
            response += "\n📈 **Trend Analysis**:\n"
            response += "```\n"
            response += self._generate_trend_chart(analysis_context['trend_indicators'])
            response += "\n```\n"
        
        return response

    def _generate_ascii_chart(self, data):
        """
        Generate ASCII chart for key metrics
        """
        # Implementation for ASCII chart generation
        chart = ""
        # Add chart generation logic here
        return chart

    def _generate_trend_chart(self, trend_data):
        """
        Generate ASCII trend chart
        """
        # Implementation for trend chart generation
        chart = ""
        # Add trend chart generation logic here
        return chart

    async def _handle_web_search_query(self, message, intent):
        """
        Handle queries that require web search
        """
        try:
            # Extract search query from message
            search_query = self._extract_search_query(message)
            
            # Check cache first
            if search_query in self.web_search_cache:
                cached_result = self.web_search_cache[search_query]
                if (datetime.now() - cached_result['timestamp']).seconds < 3600:  # 1 hour cache
                    return self._format_web_search_response(cached_result['data'], search_query)
            
            # Perform web search
            search_results = await self._perform_web_search(search_query)
            
            if search_results:
                # Cache results
                self.web_search_cache[search_query] = {
                    'data': search_results,
                    'timestamp': datetime.now()
                }
                
                return self._format_web_search_response(search_results, search_query)
            else:
                return f"I couldn't find reliable information about '{search_query}' at the moment. Could you try rephrasing your question or being more specific?"
                
        except Exception as e:
            logger.error(f"Web search error: {str(e)}")
            return "I'm having trouble accessing web search right now. Please try again later or ask about your Jira data instead."

    async def _handle_general_query(self, message, conversation_id):
        """
        Handle general conversation queries
        """
        message_lower = message.lower()
        
        # Get conversation history for context
        history = await self._get_conversation_history(conversation_id)
        
        if any(word in message_lower for word in ['hello', 'hi', 'hey']):
            return "Hello! I'm your intelligent Jira analysis assistant with web search capabilities. I can help you analyze your Jira data, search for information online, and answer various questions. What would you like to know?"
        
        elif any(word in message_lower for word in ['help', 'what can you do']):
            return """🤖 **I'm your intelligent AI assistant with these capabilities:**

🔍 **Jira Analysis**: 
• Analyze your uploaded Jira data
• Provide client insights and trends
• Generate recommendations

🌐 **Web Search**: 
• Search for current information
• Explain concepts and definitions
• Find latest news and updates

🧠 **Memory & Context**: 
• Remember our conversation
• Maintain context across queries
• Learn from your data patterns

💡 **Ask me anything like:**
• "Analyze my latest Jira data"
• "What is agile methodology?"
• "How to improve client satisfaction?"
• "Search for latest Jira best practices"
"""
        
        elif any(word in message_lower for word in ['thank', 'thanks']):
            return "You're welcome! I'm here to help with your Jira analysis and any other questions you might have. Feel free to ask me anything!"
        
        else:
            # Try to provide a helpful response or suggest web search
            return f"I understand you're asking about something general. Would you like me to search the web for information about '{message}' or would you prefer to ask about your Jira data?"

    async def _handle_hybrid_query(self, message, context_data, intent):
        """
        Handle queries that might need both Jira data and web search
        """
        response_parts = []
        
        # First, check if we have relevant Jira data
        if context_data and context_data.get('latest_analysis'):
            jira_response = await self._handle_jira_query(message, context_data, intent)
            if jira_response and "don't have any Jira" not in jira_response:
                response_parts.append("📊 **From Your Jira Data:**\n" + jira_response)
        
        # Then, try web search for additional context
        try:
            web_response = await self._handle_web_search_query(message, intent)
            if web_response and "couldn't find" not in web_response and "having trouble" not in web_response:
                response_parts.append("🌐 **Additional Information:**\n" + web_response)
        except:
            pass
        
        if response_parts:
            return "\n\n".join(response_parts)
        else:
            return "I can help you with both Jira analysis and web search. Could you be more specific about what you're looking for?"

    def _extract_search_query(self, message):
        """
        Extract search query from user message
        """
        # Remove common question words and extract the core query
        message = re.sub(r'^(what is|how to|explain|define|search for|find|tell me about)\s+', '', message.lower())
        message = re.sub(r'\?$', '', message)
        return message.strip()

    async def _perform_web_search(self, query):
        """
        Perform web search using multiple search APIs
        """
        # Try Google Custom Search API first (if available)
        google_result = await self._try_google_search(query)
        if google_result:
            return google_result

        # Try DuckDuckGo Instant Answer API
        try:
            url = f"https://api.duckduckgo.com/"
            params = {
                'q': query,
                'format': 'json',
                'no_html': '1',
                'skip_disambig': '1'
            }

            response = requests.get(url, params=params, timeout=10)

            if response.status_code == 200:
                data = response.json()

                # Extract relevant information
                result = {
                    'abstract': data.get('Abstract', ''),
                    'definition': data.get('Definition', ''),
                    'answer': data.get('Answer', ''),
                    'related_topics': data.get('RelatedTopics', [])[:3],
                    'source': data.get('AbstractSource', 'DuckDuckGo')
                }

                if any(result.values()):  # If we got any useful data
                    return result

        except Exception as e:
            logger.error(f"DuckDuckGo search failed: {str(e)}")

        # Fallback: Use built-in knowledge base
        try:
            return await self._get_definition_fallback(query)
        except Exception as e:
            logger.error(f"Fallback search failed: {str(e)}")
            return None

    async def _try_google_search(self, query):
        """
        Try Google Custom Search API with improved error handling and configuration
        """
        try:
            google_api_key = os.getenv('GOOGLE_API_KEY', 'AIzaSyDLS7KjnBml_w1PJEMvtnrLyNwhQXJQpc0')

            # Use a working Custom Search Engine ID or create a general web search
            google_cse_id = os.getenv('GOOGLE_CSE_ID', '017576662512468239146:omuauf_lfve')

            if not google_api_key:
                logger.warning("Google API key not available")
                return None

            logger.info(f"Attempting Google search for: {query}")

            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': google_api_key,
                'cx': google_cse_id,
                'q': query,
                'num': 5,  # Get more results
                'safe': 'active'
            }

            response = requests.get(url, params=params, timeout=15)

            logger.info(f"Google API response status: {response.status_code}")

            if response.status_code == 200:
                data = response.json()
                items = data.get('items', [])

                logger.info(f"Google search returned {len(items)} results")

                if items:
                    # Format the first result
                    first_result = items[0]
                    result = {
                        'abstract': first_result.get('snippet', ''),
                        'title': first_result.get('title', ''),
                        'link': first_result.get('link', ''),
                        'source': 'Google Search',
                        'query': query,
                        'total_results': len(items),
                        'additional_results': [
                            {
                                'title': item.get('title', ''),
                                'snippet': item.get('snippet', ''),
                                'link': item.get('link', '')
                            } for item in items[1:4]  # Get 3 additional results
                        ]
                    }

                    logger.info(f"Successfully formatted Google search result for: {query}")
                    return result
                else:
                    logger.warning(f"No search results found for: {query}")
            else:
                error_data = response.json() if response.content else {}
                logger.error(f"Google API error {response.status_code}: {error_data}")

                # Check for specific API errors
                if response.status_code == 403:
                    logger.error("Google API quota exceeded or invalid API key")
                elif response.status_code == 400:
                    logger.error("Invalid search parameters")

        except requests.exceptions.Timeout:
            logger.error("Google search request timed out")
        except requests.exceptions.RequestException as e:
            logger.error(f"Google search request failed: {str(e)}")
        except Exception as e:
            logger.error(f"Unexpected Google search error: {str(e)}")

        return None

    async def _get_definition_fallback(self, query):
        """
        Fallback method to provide definitions and explanations
        """
        # Enhanced knowledge base for common terms
        knowledge_base = {
            'agile': {
                'definition': 'Agile is a project management methodology that emphasizes iterative development, collaboration, and flexibility.',
                'details': 'Agile methodologies include Scrum, Kanban, and XP, focusing on delivering working software in short iterations with continuous feedback and adaptation.'
            },
            'scrum': {
                'definition': 'Scrum is an agile framework for managing product development with roles, events, and artifacts.',
                'details': 'Key roles include Product Owner, Scrum Master, and Development Team. Events include Sprint Planning, Daily Standups, Sprint Review, and Retrospective.'
            },
            'kanban': {
                'definition': 'Kanban is a visual workflow management method that helps teams visualize work and optimize flow.',
                'details': 'Uses boards with columns representing workflow stages and cards representing work items. Focuses on limiting work in progress (WIP).'
            },
            'jira': {
                'definition': 'Jira is a project management and issue tracking software developed by Atlassian.',
                'details': 'Widely used for bug tracking, agile project management, and software development workflows. Supports Scrum and Kanban methodologies.'
            },
            'devops': {
                'definition': 'DevOps is a set of practices that combines software development and IT operations.',
                'details': 'Aims to shorten the development lifecycle and provide continuous delivery with high software quality through automation and monitoring.'
            },
            'ci/cd': {
                'definition': 'CI/CD stands for Continuous Integration and Continuous Deployment/Delivery.',
                'details': 'CI involves automatically testing code changes, while CD automates the deployment process to production environments.'
            },
            'sprint': {
                'definition': 'A Sprint is a time-boxed iteration in Scrum, typically lasting 1-4 weeks.',
                'details': 'During a sprint, the team works to complete a set of planned work items and deliver a potentially shippable product increment.'
            },
            'backlog': {
                'definition': 'A backlog is a prioritized list of features, user stories, or tasks to be completed.',
                'details': 'In Scrum, the Product Backlog contains all desired work on the project, prioritized by the Product Owner.'
            },
            'user story': {
                'definition': 'A user story is a short description of a feature from the perspective of the end user.',
                'details': 'Typically follows the format: "As a [user type], I want [functionality] so that [benefit]".'
            },
            'epic': {
                'definition': 'An epic is a large user story that can be broken down into smaller stories.',
                'details': 'Epics are used to organize work and track progress on larger features or initiatives.'
            }
        }

        query_lower = query.lower()
        for term, info in knowledge_base.items():
            if term in query_lower:
                return {
                    'definition': info['definition'],
                    'abstract': info['details'],
                    'source': 'Built-in Knowledge Base'
                }

        return None

    def _format_web_search_response(self, search_data, query):
        """
        Format web search results into a readable response
        """
        if not search_data:
            return f"I couldn't find specific information about '{query}'."

        response = f"🔍 **Search Results for '{query}':**\n\n"

        # Handle Google search results
        if search_data.get('source') == 'Google Search':
            if search_data.get('title'):
                response += f"📰 **{search_data['title']}**\n"

            if search_data.get('abstract'):
                response += f"📝 {search_data['abstract']}\n\n"

            if search_data.get('link'):
                response += f"🔗 **Link**: {search_data['link']}\n\n"

            # Add additional results
            if search_data.get('additional_results'):
                response += "📚 **Related Results**:\n"
                for result in search_data['additional_results']:
                    if result.get('title') and result.get('snippet'):
                        response += f"• **{result['title']}**: {result['snippet'][:100]}...\n"
                response += "\n"

        # Handle DuckDuckGo results
        else:
            # Add definition if available
            if search_data.get('definition'):
                response += f"📖 **Definition**: {search_data['definition']}\n\n"

            # Add abstract/summary
            if search_data.get('abstract'):
                response += f"📝 **Summary**: {search_data['abstract']}\n\n"

            # Add direct answer if available
            if search_data.get('answer'):
                response += f"💡 **Answer**: {search_data['answer']}\n\n"

        # Add source
        if search_data.get('source'):
            response += f"📚 **Source**: {search_data['source']}\n"

        return response.strip()

    async def _try_llm_response(self, message, context_data):
        """
        Try to get response from LLM models
        """
        for model_info in self.llm_models:
            try:
                response = await self._make_llm_request(model_info, message, context_data)
                if response and len(response.strip()) > 20:
                    self.current_model = model_info
                    return response
            except Exception as e:
                logger.warning(f"LLM model {model_info['name']} failed: {str(e)}")
                continue

        return None

    async def _make_llm_request(self, model_info, message, context_data):
        """
        Make request to LLM model
        """
        headers = {"Content-Type": "application/json"}
        if self.hf_token:
            headers["Authorization"] = f"Bearer {self.hf_token}"

        # Prepare context-aware prompt
        prompt = self._prepare_intelligent_prompt(message, context_data)

        payload = {
            "inputs": prompt,
            "parameters": {
                "max_new_tokens": 300,
                "temperature": 0.7,
                "do_sample": True,
                "return_full_text": False
            }
        }

        response = requests.post(model_info["api_url"], headers=headers, json=payload, timeout=30)

        if response.status_code == 200:
            result = response.json()
            if isinstance(result, list) and len(result) > 0:
                return result[0].get("generated_text", "").strip()

        return None

    def _prepare_intelligent_prompt(self, message, context_data):
        """
        Prepare an intelligent prompt with context
        """
        prompt = f"{self.system_prompt}\n\n"

        # Add database context if available
        if context_data and context_data.get('latest_analysis'):
            analysis = context_data['latest_analysis']
            prompt += f"Current Jira Analysis Context:\n"
            prompt += f"- File: {analysis.get('file_name', 'Unknown')}\n"
            prompt += f"- Total Issues: {analysis.get('issue_count', 0)}\n"
            prompt += f"- Top Priority: {context_data.get('data_summary', {}).get('top_priority', 'N/A')}\n"
            prompt += f"- Clients: {context_data.get('data_summary', {}).get('total_clients', 0)}\n\n"

        prompt += f"User Question: {message}\n"
        prompt += f"Assistant Response:"

        return prompt

    async def _update_memory(self, conversation_id, context_data):
        """
        Update memory with comprehensive context and database state
        """
        if conversation_id:
            self.conversation_memory[conversation_id] = {
                'last_updated': datetime.now(),
                'context_data': context_data,
                'query_count': self.conversation_memory.get(conversation_id, {}).get('query_count', 0) + 1
            }

        # Update comprehensive database memory with ALL analysis data
        if context_data:
            # Store comprehensive user data
            self.database_memory['comprehensive_data'] = {
                'user_overview': context_data.get('user_overview', {}),
                'client_analytics': context_data.get('client_analytics', {}),
                'temporal_analysis': context_data.get('temporal_analysis', {}),
                'sentiment_overview': context_data.get('sentiment_overview', {}),
                'priority_insights': context_data.get('priority_insights', {}),
                'performance_metrics': context_data.get('performance_metrics', {}),
                'last_updated': datetime.now()
            }

            # Store historical trends for comparative analysis
            if context_data.get('temporal_analysis', {}).get('timeline'):
                timeline = context_data['temporal_analysis']['timeline']
                self.database_memory['historical_timeline'] = {
                    'data': timeline,
                    'analysis_count': len(timeline),
                    'date_range': context_data['temporal_analysis'].get('date_range', {}),
                    'last_updated': datetime.now()
                }

            # Store client insights for proactive management
            if context_data.get('client_analytics', {}).get('all_clients'):
                clients = context_data['client_analytics']['all_clients']
                self.database_memory['client_insights'] = {
                    'total_clients': len(clients),
                    'critical_clients': self._identify_critical_clients_from_data(clients),
                    'client_trends': self._analyze_client_trends(clients),
                    'last_updated': datetime.now()
                }

    async def _store_in_memory(self, conversation_id, user_message, response):
        """
        Store conversation in memory for context
        """
        if conversation_id and conversation_id in self.conversation_memory:
            if 'messages' not in self.conversation_memory[conversation_id]:
                self.conversation_memory[conversation_id]['messages'] = []

            self.conversation_memory[conversation_id]['messages'].append({
                'user': user_message,
                'assistant': response,
                'timestamp': datetime.now()
            })

            # Keep only last 10 messages to manage memory
            if len(self.conversation_memory[conversation_id]['messages']) > 10:
                self.conversation_memory[conversation_id]['messages'] = \
                    self.conversation_memory[conversation_id]['messages'][-10:]

    async def _get_conversation_history(self, conversation_id):
        """
        Get conversation history from memory
        """
        if conversation_id and conversation_id in self.conversation_memory:
            return self.conversation_memory[conversation_id].get('messages', [])
        return []

    def _identify_critical_clients_from_data(self, clients_data):
        """
        Identify critical clients from comprehensive client data
        """
        critical_clients = []

        for client_name, data in clients_data.items():
            risk_score = 0
            risk_factors = []

            # Check average sentiment
            if data.get('avg_sentiment') and data['avg_sentiment'] < -0.3:
                risk_score += 2
                risk_factors.append(f"Poor sentiment: {data['avg_sentiment']:.2f}")

            # Check experience score decline
            if data.get('avg_experience_score') and data['avg_experience_score'] < 3.0:
                risk_score += 1
                risk_factors.append(f"Low experience score: {data['avg_experience_score']:.1f}")

            # Check ticket volume
            if data.get('total_tickets', 0) > 50:  # High volume threshold
                risk_score += 1
                risk_factors.append(f"High ticket volume: {data['total_tickets']}")

            # Check recent activity
            if data.get('ticket_history'):
                recent_tickets = [t for t in data['ticket_history'] if
                                (datetime.now() - t['date']).days <= 30]
                if len(recent_tickets) > 10:
                    risk_score += 1
                    risk_factors.append("High recent activity")

            if risk_score >= 2:
                critical_clients.append({
                    'client_name': client_name,
                    'risk_score': risk_score,
                    'risk_factors': risk_factors,
                    'total_tickets': data.get('total_tickets', 0),
                    'avg_sentiment': data.get('avg_sentiment', 0),
                    'avg_experience_score': data.get('avg_experience_score', 0)
                })

        return sorted(critical_clients, key=lambda x: x['risk_score'], reverse=True)

    def _analyze_client_trends(self, clients_data):
        """
        Analyze trends across all clients
        """
        trends = {
            'sentiment_declining': [],
            'volume_increasing': [],
            'experience_declining': [],
            'new_clients': [],
            'stable_clients': []
        }

        for client_name, data in clients_data.items():
            # Analyze sentiment trend
            if data.get('sentiment_scores') and len(data['sentiment_scores']) > 1:
                recent_sentiment = data['sentiment_scores'][-1]
                older_sentiment = data['sentiment_scores'][0]
                if recent_sentiment < older_sentiment - 0.2:
                    trends['sentiment_declining'].append(client_name)

            # Analyze volume trend
            if data.get('ticket_history') and len(data['ticket_history']) > 1:
                recent_period = [t for t in data['ticket_history'] if
                               (datetime.now() - t['date']).days <= 30]
                older_period = [t for t in data['ticket_history'] if
                              30 < (datetime.now() - t['date']).days <= 60]

                if recent_period and older_period:
                    recent_avg = sum(t['tickets'] for t in recent_period) / len(recent_period)
                    older_avg = sum(t['tickets'] for t in older_period) / len(older_period)
                    if recent_avg > older_avg * 1.5:
                        trends['volume_increasing'].append(client_name)

            # Check if new client (first seen recently)
            if data.get('first_seen'):
                days_since_first = (datetime.now() - data['first_seen']).days
                if days_since_first <= 30:
                    trends['new_clients'].append(client_name)
                elif (data.get('avg_sentiment', 0) > -0.1 and
                      data.get('total_tickets', 0) < 20):
                    trends['stable_clients'].append(client_name)

        return trends

    def get_memory_status(self):
        """
        Get current memory status for debugging
        """
        deepseek_status = "Not Available"
        if self.deepseek_handler:
            if self.deepseek_handler.is_model_ready():
                model_info = self.deepseek_handler.get_model_info()
                deepseek_status = f"Ready ({model_info.get('method', 'unknown')})"
            else:
                deepseek_status = "Failed to Load"

        return {
            'active_conversations': len(self.conversation_memory),
            'database_memory': bool(self.database_memory.get('latest_analysis')),
            'web_cache_size': len(self.web_search_cache),
            'deepseek_model': deepseek_status,
            'current_model': self.current_model['name'] if self.current_model else 'Fallback',
            'google_search': bool(os.getenv('GOOGLE_API_KEY'))
        }

    async def _get_llm_response(self, prompt):
        """
        Get response from the LLM model
        """
        try:
            # Try DeepSeek model first if available
            if self.deepseek_handler and self.deepseek_handler.is_model_ready():
                response = await self.deepseek_handler.generate_response(prompt)
                if response:
                    return response

            # Try fallback models
            for model_info in self.llm_models:
                try:
                    response = await self._make_llm_request(model_info, prompt, None)
                    if response:
                        return response
                except Exception as e:
                    logger.warning(f"Fallback model {model_info['name']} failed: {str(e)}")
                    continue

            # If all models fail, return a default response
            return "I apologize, but I'm having trouble generating a response at the moment. Please try again."

        except Exception as e:
            logger.error(f"Error getting LLM response: {str(e)}")
            return "I encountered an error while processing your request. Please try again."
