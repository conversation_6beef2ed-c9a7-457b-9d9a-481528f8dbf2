import json
import logging
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import ensure_csrf_cookie
from django.contrib.auth.decorators import login_required
from .intelligent_agent import IntelligentAgent

logger = logging.getLogger(__name__)

@ensure_csrf_cookie
@login_required
@require_http_methods(["POST"])
async def chat_message(request):
    """
    Handle chat messages and return AI responses with insights
    """
    try:
        # Parse request data
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            logger.error("Invalid JSON in request body")
            return JsonResponse({
                'error': 'Invalid request format'
            }, status=400)

        message = data.get('message', '').strip()
        conversation_id = data.get('conversation_id')
        
        if not message:
            return JsonResponse({
                'error': 'Message is required'
            }, status=400)

        # Initialize intelligent agent with the authenticated user
        try:
            agent = IntelligentAgent(request.user)
        except Exception as e:
            logger.error(f"Error initializing IntelligentAgent: {str(e)}")
            return JsonResponse({
                'error': 'Failed to initialize AI agent'
            }, status=500)

        # Process message and get response
        try:
            response = await agent.process_message(
                message=message,
                conversation_id=conversation_id,
                context_data=agent.analysis_cache
            )
        except Exception as e:
            logger.error(f"Error processing message: {str(e)}")
            return JsonResponse({
                'error': 'Failed to process message'
            }, status=500)

        # Get insights data
        try:
            insights = {
                'critical_clients': agent.llm_handler.identify_critical_clients(agent.analysis_cache),
                'risk_indicators': agent.risk_assessment_cache,
                'trends': agent.analysis_cache.get('trends', {})
            }
        except Exception as e:
            logger.error(f"Error getting insights: {str(e)}")
            insights = {
                'critical_clients': [],
                'risk_indicators': {},
                'trends': {}
            }

        return JsonResponse({
            'response': response,
            'insights': insights,
            'conversation_id': conversation_id or str(request.user.id)
        })

    except Exception as e:
        logger.error(f"Unexpected error in chat_message: {str(e)}")
        return JsonResponse({
            'error': 'Internal server error'
        }, status=500)