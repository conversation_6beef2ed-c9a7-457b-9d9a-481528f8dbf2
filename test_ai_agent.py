#!/usr/bin/env python
"""
Test script for AI Agent functionality
Tests all the critical components we've fixed
"""
import os
import sys
import django
import asyncio
from datetime import datetime

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'jira_analyzer.settings')
django.setup()

from django.contrib.auth.models import User
from analyzer.intelligent_agent import IntelligentAgent
from analyzer.database_integration import DatabaseIntegrator
from analyzer.deepseek_llm_handler import DeepSeekLLMHandler
from asgiref.sync import sync_to_async

async def test_ai_agent():
    """Test the AI Agent functionality"""
    print("🤖 Testing AI Agent Functionality")
    print("=" * 50)
    
    # Get a test user
    try:
        user = await sync_to_async(User.objects.first)()
        if not user:
            print("❌ No users found in database")
            return False
        print(f"✅ Using test user: {user.username}")
    except Exception as e:
        print(f"❌ Error getting user: {str(e)}")
        return False
    
    # Test 1: Database Integration
    print("\n📊 Testing Database Integration...")
    try:
        db_integrator = DatabaseIntegrator(user)
        data = await db_integrator.get_comprehensive_user_data()
        print(f"✅ Database integration working")
        print(f"   - User overview: {bool(data.get('user_overview'))}")
        print(f"   - Client analytics: {bool(data.get('client_analytics'))}")
        print(f"   - Temporal analysis: {bool(data.get('temporal_analysis'))}")
        print(f"   - Sentiment overview: {bool(data.get('sentiment_overview'))}")
    except Exception as e:
        print(f"❌ Database integration error: {str(e)}")
        return False
    
    # Test 2: DeepSeek LLM Handler
    print("\n🧠 Testing DeepSeek LLM Handler...")
    try:
        llm_handler = DeepSeekLLMHandler()
        model_info = llm_handler.get_model_info()
        print(f"✅ DeepSeek handler initialized")
        print(f"   - Model: {model_info.get('name')}")
        print(f"   - Type: {model_info.get('type')}")
        print(f"   - Ready: {model_info.get('is_loaded')}")
        print(f"   - Ollama available: {model_info.get('ollama_available')}")
        
        # Test response generation
        if llm_handler.is_model_ready():
            test_response = await llm_handler.generate_response(
                "What is Jira?", 
                context_data=data
            )
            print(f"✅ Generated test response: {test_response[:100]}...")
        else:
            print("⚠️  Model not ready, but fallback should work")
    except Exception as e:
        print(f"❌ DeepSeek handler error: {str(e)}")
        return False
    
    # Test 3: Intelligent Agent
    print("\n🎯 Testing Intelligent Agent...")
    try:
        agent = IntelligentAgent(user)
        
        # Test memory status
        memory_status = agent.get_memory_status()
        print(f"✅ Intelligent agent initialized")
        print(f"   - Active conversations: {memory_status.get('active_conversations')}")
        print(f"   - Database memory: {memory_status.get('database_memory')}")
        print(f"   - Web cache size: {memory_status.get('web_cache_size')}")
        print(f"   - DeepSeek model: {memory_status.get('deepseek_model')}")
        print(f"   - Google search: {memory_status.get('google_search')}")
        
        # Test message processing
        test_message = "What is the current status of my Jira analysis?"
        response = await agent.process_message(
            message=test_message,
            conversation_id="test_conversation",
            context_data=data
        )
        print(f"✅ Processed test message successfully")
        print(f"   - Response length: {len(response)} characters")
        print(f"   - Response preview: {response[:150]}...")
        
    except Exception as e:
        print(f"❌ Intelligent agent error: {str(e)}")
        return False
    
    # Test 4: Web Search (if available)
    print("\n🌐 Testing Web Search...")
    try:
        google_api_key = os.getenv('GOOGLE_API_KEY')
        if google_api_key:
            search_response = await agent._try_google_search("What is agile methodology?")
            if search_response:
                print(f"✅ Google search working")
                print(f"   - Title: {search_response.get('title', 'N/A')}")
                print(f"   - Source: {search_response.get('source', 'N/A')}")
            else:
                print("⚠️  Google search configured but no results")
        else:
            print("⚠️  Google API key not configured")
    except Exception as e:
        print(f"❌ Web search error: {str(e)}")
    
    # Test 5: Memory System
    print("\n🧠 Testing Memory System...")
    try:
        # Test conversation memory
        await agent._store_in_memory("test_conversation", test_message, response)
        history = await agent._get_conversation_history("test_conversation")
        print(f"✅ Memory system working")
        print(f"   - Stored messages: {len(history)}")
        
        # Test database memory
        if agent.database_memory.get('comprehensive_data'):
            print(f"   - Database memory: Active")
            print(f"   - Last updated: {agent.database_memory['comprehensive_data'].get('last_updated')}")
        else:
            print(f"   - Database memory: Empty")
            
    except Exception as e:
        print(f"❌ Memory system error: {str(e)}")
        return False
    
    print("\n🎉 AI Agent Test Summary")
    print("=" * 50)
    print("✅ All core components are working!")
    print("✅ Database integration: Functional")
    print("✅ LLM handler: Functional") 
    print("✅ Intelligent agent: Functional")
    print("✅ Memory system: Functional")
    print("✅ Web search: Configured")
    
    return True

def test_ollama_connection():
    """Test Ollama connection and models"""
    print("\n🔧 Testing Ollama Connection...")
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✅ Ollama connected - {len(models)} models available")
            for model in models:
                print(f"   - {model.get('name', 'Unknown')}")
            return True
        else:
            print(f"❌ Ollama responded with status {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Ollama connection error: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 Starting AI Agent Comprehensive Test")
    print("=" * 50)
    
    # Test Ollama first
    ollama_ok = test_ollama_connection()
    
    # Test AI Agent
    try:
        result = asyncio.run(test_ai_agent())
        if result:
            print("\n🎯 RESULT: AI Agent is fully functional!")
            print("🔥 Ready for production use!")
        else:
            print("\n❌ RESULT: Some issues detected")
            print("🔧 Check the errors above for details")
    except Exception as e:
        print(f"\n💥 CRITICAL ERROR: {str(e)}")
        print("🔧 Check your Django setup and database")
