import logging
import requests
import json
from dotenv import load_dotenv
from datetime import datetime, timedelta

# Set up logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class DeepSeekLLMHandler:
    """
    Enhanced LLM handler using Ollama DeepSeek 1.5B model for local inference
    with advanced client management capabilities
    """

    def __init__(self):
        # Ollama configuration
        self.ollama_base_url = "http://localhost:11434"
        self.ollama_api_url = f"{self.ollama_base_url}/api"

        # Model configuration with priority order
        self.model_candidates = [
            {
                "name": "deepseek-coder:1.5b",
                "type": "deepseek_coder",
                "description": "DeepSeek Coder 1.5B (Ollama)",
                "ollama_model": True
            },
            {
                "name": "deepseek-coder:1.3b",
                "type": "deepseek_coder",
                "description": "DeepSeek Coder 1.3B (Ollama)",
                "ollama_model": True
            },
            {
                "name": "deepseek-coder:latest",
                "type": "deepseek_latest",
                "description": "DeepSeek Coder Latest (Ollama)",
                "ollama_model": True
            }
        ]

        self.current_model = None
        self.model_type = None
        self.max_tokens = 2048
        self.is_loaded = False
        self.ollama_available = False

        # Client management configuration
        self.client_risk_thresholds = {
            'sentiment': -0.5,
            'ticket_volume_increase': 0.2,  # 20% increase
            'critical_priority_ratio': 0.3,  # 30% of tickets
            'experience_score_decline': 0.1  # 10% decline
        }

        # Email templates
        self.email_templates = {
            'apologetic': {
                'subject': "Addressing Recent Issues - {client_name}",
                'template': """Dear {client_name},

I hope this email finds you well. I wanted to personally reach out regarding the recent challenges you've been experiencing with our services.

Based on our analysis of your recent tickets, we've identified {issue_summary}. We understand the impact this has had on your operations and want to assure you that we're taking immediate action.

Here's what we're doing to address these concerns:
{action_plan}

We value your partnership and are committed to improving your experience. Would you be available for a brief call this week to discuss these matters in more detail?

Best regards,
{user_name}"""
            },
            'solution_focused': {
                'subject': "Proactive Solutions for {client_name}",
                'template': """Dear {client_name},

I hope you're doing well. I'm reaching out with some proactive solutions based on our analysis of your recent service interactions.

We've identified several opportunities to enhance your experience:
{solution_points}

Our team has prepared a detailed action plan:
{action_plan}

Would you be interested in scheduling a review meeting to discuss these improvements?

Best regards,
{user_name}"""
            },
            'escalation': {
                'subject': "Urgent: Action Required - {client_name}",
                'template': """Dear {client_name},

I'm writing to address some critical concerns that require immediate attention.

Our analysis has identified several high-priority issues:
{critical_issues}

We've escalated these matters to our senior management team and have prepared an immediate action plan:
{action_plan}

I would appreciate the opportunity to discuss this situation with you directly. Please let me know your availability for an urgent call.

Best regards,
{user_name}"""
            },
            'check_in': {
                'subject': "Client Check-in: {client_name}",
                'template': """Dear {client_name},

I hope this email finds you well. I wanted to check in regarding your recent experience with our services.

Based on our analysis:
{status_summary}

We're committed to ensuring your satisfaction and would appreciate your feedback on:
{feedback_points}

Would you be available for a brief call to discuss these points?

Best regards,
{user_name}"""
            }
        }

        # Initialize Ollama connection
        self._initialize_ollama()

    def _initialize_ollama(self):
        """
        Initialize Ollama connection and find the best available model
        """
        try:
            # Check if Ollama service is running
            response = requests.get(f"{self.ollama_api_url}/tags", timeout=5)
            if response.status_code == 200:
                self.ollama_available = True
                available_models = response.json().get('models', [])
                logger.info(f"Ollama service available with {len(available_models)} models")

                # Find the best available model
                self._select_best_model(available_models)
            else:
                logger.warning(f"Ollama service responded with status {response.status_code}")
                self.ollama_available = False

        except requests.exceptions.RequestException as e:
            logger.warning(f"Ollama service not available: {str(e)}")
            self.ollama_available = False

        except Exception as e:
            logger.error(f"Error initializing Ollama: {str(e)}")
            self.ollama_available = False

    def _select_best_model(self, available_models):
        """
        Select the best available model from Ollama
        """
        available_model_names = [model.get('name', '') for model in available_models]
        logger.info(f"Available Ollama models: {available_model_names}")

        # Try to find the best model from our candidates
        for candidate in self.model_candidates:
            model_name = candidate["name"]
            if model_name in available_model_names:
                self.current_model = model_name
                self.model_type = candidate["type"]
                self.is_loaded = True
                logger.info(f"✅ Selected Ollama model: {candidate['description']}")
                return

        # If no preferred models found, try to pull DeepSeek Coder
        logger.info("No preferred models found, attempting to pull deepseek-coder:1.5b")
        if self._pull_model("deepseek-coder:1.5b"):
            self.current_model = "deepseek-coder:1.5b"
            self.model_type = "deepseek_coder"
            self.is_loaded = True
            logger.info("✅ Successfully pulled and selected deepseek-coder:1.5b")
        else:
            logger.warning("Failed to pull DeepSeek model, will use fallback")
            self.is_loaded = False

    def _pull_model(self, model_name):
        """
        Pull a model using Ollama API
        """
        try:
            logger.info(f"Pulling Ollama model: {model_name}")
            response = requests.post(
                f"{self.ollama_api_url}/pull",
                json={"name": model_name},
                timeout=300  # 5 minutes timeout for model pulling
            )

            if response.status_code == 200:
                logger.info(f"Successfully pulled model: {model_name}")
                return True
            else:
                logger.error(f"Failed to pull model {model_name}: {response.status_code}")
                return False

        except Exception as e:
            logger.error(f"Error pulling model {model_name}: {str(e)}")
            return False

    async def generate_response(self, user_message, conversation_id=None, context_data=None):
        """
        Generate response using Ollama DeepSeek model
        """
        if not self.ollama_available or not self.is_loaded:
            logger.warning("Ollama DeepSeek model not available, using fallback")
            return self._get_fallback_response(user_message, context_data)

        try:
            # Prepare the prompt with context
            prompt = self._prepare_prompt(user_message, context_data)

            # Make request to Ollama API
            response = await self._call_ollama_api(prompt)

            if response:
                return self._clean_response(response, user_message)
            else:
                logger.warning("Ollama API returned empty response, using fallback")
                return self._get_fallback_response(user_message, context_data)

        except Exception as e:
            logger.error(f"Error generating response with Ollama: {str(e)}")
            return self._get_fallback_response(user_message, context_data)

    async def _call_ollama_api(self, prompt):
        """
        Call Ollama API for text generation
        """
        try:
            payload = {
                "model": self.current_model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "num_predict": self.max_tokens,
                    "stop": ["User:", "Human:", "\n\n"]
                }
            }

            logger.info(f"Calling Ollama API with model: {self.current_model}")

            response = requests.post(
                f"{self.ollama_api_url}/generate",
                json=payload,
                timeout=60  # 60 seconds timeout
            )

            if response.status_code == 200:
                result = response.json()
                generated_text = result.get('response', '').strip()

                if generated_text:
                    logger.info(f"Ollama API response received ({len(generated_text)} chars)")
                    return generated_text
                else:
                    logger.warning("Ollama API returned empty response")
                    return None
            else:
                logger.error(f"Ollama API error: {response.status_code} - {response.text}")
                return None

        except requests.exceptions.Timeout:
            logger.error("Ollama API request timed out")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"Ollama API request failed: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error calling Ollama API: {str(e)}")
            return None

    def _prepare_prompt(self, user_message, context_data):
        """
        Prepare prompt with context for Ollama
        """
        prompt = """You are an advanced AI assistant specialized in proactive client management and Jira analysis.
        You can:
        1. Identify critical client situations
        2. Generate professional email communications
        3. Provide strategic recommendations
        4. Analyze patterns and trends
        5. Suggest preventive actions

        Always be professional, data-driven, and solution-focused in your responses.
        """

        if context_data:
            # Add client analysis context
            if context_data.get('client_metrics'):
                prompt += "\nClient Analysis Context:\n"
                for client, metrics in context_data['client_metrics'].items():
                    prompt += f"- {client}:\n"
                    prompt += f"  * Sentiment: {metrics.get('sentiment', 'N/A')}\n"
                    prompt += f"  * Ticket Volume: {metrics.get('ticket_count', 0)}\n"
                    prompt += f"  * Experience Score: {metrics.get('experience_score', 'N/A')}\n"
                    prompt += f"  * Priority Distribution: {metrics.get('priority_distribution', {})}\n"

            # Add trend analysis
            if context_data.get('trends'):
                prompt += "\nTrend Analysis:\n"
                for trend, data in context_data['trends'].items():
                    prompt += f"- {trend}: {data}\n"

            # Add risk indicators
            if context_data.get('risk_indicators'):
                prompt += "\nRisk Indicators:\n"
                for indicator, value in context_data['risk_indicators'].items():
                    prompt += f"- {indicator}: {value}\n"

        prompt += f"\nUser Query: {user_message}\n\nAssistant:"
        return prompt

    def _clean_response(self, response, user_message):
        """
        Clean and format the Ollama response
        """
        # Remove any repetition of the user message
        if user_message.lower() in response.lower():
            response = response.replace(user_message, "").strip()

        # Remove common artifacts
        response = response.replace("User:", "").replace("Assistant:", "").strip()

        # Remove any system prompts that might leak through
        response = response.replace("You are an advanced AI assistant", "").strip()

        # Limit response length
        if len(response) > 1500:
            response = response[:1500] + "..."

        return response.strip()

    def _get_fallback_response(self, user_message, context_data):
        """
        Provide fallback response when Ollama model is not available
        """
        # Import the existing fallback system
        try:
            from .opensource_llm_integration import OpenSourceLLMHandler
            fallback_handler = OpenSourceLLMHandler()
            return fallback_handler._get_fallback_response(user_message, context_data)
        except Exception as e:
            logger.error(f"Fallback system error: {str(e)}")
            return "I'm currently experiencing technical difficulties. Please try again later or rephrase your question."

    def get_model_info(self):
        """
        Get information about the current Ollama model
        """
        return {
            "name": self.current_model or "Not loaded",
            "type": self.model_type or "Unknown",
            "is_loaded": self.is_loaded,
            "ollama_available": self.ollama_available,
            "method": "ollama_api" if self.ollama_available else "fallback",
            "base_url": self.ollama_base_url
        }

    def is_model_ready(self):
        """
        Check if the Ollama model is ready for inference
        """
        return self.ollama_available and self.is_loaded

    def identify_critical_clients(self, context_data):
        """
        Identify clients requiring immediate attention based on multiple factors
        """
        critical_clients = []
        
        if not context_data or not context_data.get('client_metrics'):
            return critical_clients

        for client, metrics in context_data['client_metrics'].items():
            risk_score = 0
            risk_factors = []

            # Check sentiment score
            sentiment = metrics.get('sentiment', 0)
            if sentiment < self.client_risk_thresholds['sentiment']:
                risk_score += 1
                risk_factors.append(f"Negative sentiment score: {sentiment}")

            # Check ticket volume increase
            volume_increase = metrics.get('ticket_volume_increase', 0)
            if volume_increase > self.client_risk_thresholds['ticket_volume_increase']:
                risk_score += 1
                risk_factors.append(f"Ticket volume increased by {volume_increase*100}%")

            # Check critical priority ratio
            priority_dist = metrics.get('priority_distribution', {})
            critical_count = priority_dist.get('Critical', 0) + priority_dist.get('High', 0)
            total_tickets = sum(priority_dist.values())
            if total_tickets > 0:
                critical_ratio = critical_count / total_tickets
                if critical_ratio > self.client_risk_thresholds['critical_priority_ratio']:
                    risk_score += 1
                    risk_factors.append(f"High critical priority ratio: {critical_ratio*100}%")

            # Check experience score decline
            experience_score = metrics.get('experience_score', 0)
            if experience_score < self.client_risk_thresholds['experience_score_decline']:
                risk_score += 1
                risk_factors.append(f"Declining experience score: {experience_score}")

            if risk_score >= 2:  # Client needs attention if they have 2 or more risk factors
                critical_clients.append({
                    'client_name': client,
                    'risk_score': risk_score,
                    'risk_factors': risk_factors,
                    'metrics': metrics
                })

        # Sort by risk score
        critical_clients.sort(key=lambda x: x['risk_score'], reverse=True)
        return critical_clients

    def generate_proactive_email(self, client_data, template_type='solution_focused'):
        """
        Generate a proactive email for a client based on their data
        """
        if not client_data or template_type not in self.email_templates:
            return None

        template = self.email_templates[template_type]
        client_name = client_data['client_name']
        metrics = client_data['metrics']

        # Prepare email content
        if template_type == 'apologetic':
            issue_summary = self._generate_issue_summary(metrics)
            action_plan = self._generate_action_plan(metrics)
        elif template_type == 'solution_focused':
            solution_points = self._generate_solution_points(metrics)
            action_plan = self._generate_action_plan(metrics)
        elif template_type == 'escalation':
            critical_issues = self._generate_critical_issues(metrics)
            action_plan = self._generate_action_plan(metrics)
        else:  # check_in
            status_summary = self._generate_status_summary(metrics)
            feedback_points = self._generate_feedback_points(metrics)

        # Format the email
        email_content = template['template'].format(
            client_name=client_name,
            issue_summary=issue_summary if template_type == 'apologetic' else '',
            action_plan=action_plan if template_type in ['apologetic', 'solution_focused', 'escalation'] else '',
            solution_points=solution_points if template_type == 'solution_focused' else '',
            critical_issues=critical_issues if template_type == 'escalation' else '',
            status_summary=status_summary if template_type == 'check_in' else '',
            feedback_points=feedback_points if template_type == 'check_in' else '',
            user_name="Your Name"  # This should be replaced with actual user name
        )

        return {
            'subject': template['subject'].format(client_name=client_name),
            'content': email_content
        }

    def _generate_issue_summary(self, metrics):
        """Generate a summary of issues for apologetic emails"""
        issues = []
        if metrics.get('sentiment', 0) < 0:
            issues.append("some concerns about service quality")
        if metrics.get('ticket_volume_increase', 0) > 0:
            issues.append("an increase in support tickets")
        if metrics.get('priority_distribution', {}).get('Critical', 0) > 0:
            issues.append("some critical priority issues")
        return " and ".join(issues)

    def _generate_action_plan(self, metrics):
        """Generate an action plan based on client metrics"""
        actions = []
        if metrics.get('sentiment', 0) < 0:
            actions.append("1. Conducting a thorough review of recent interactions")
        if metrics.get('ticket_volume_increase', 0) > 0:
            actions.append("2. Implementing additional support resources")
        if metrics.get('priority_distribution', {}).get('Critical', 0) > 0:
            actions.append("3. Prioritizing critical issues for immediate resolution")
        return "\n".join(actions)

    def _generate_solution_points(self, metrics):
        """Generate solution points for solution-focused emails"""
        solutions = []
        if metrics.get('sentiment', 0) < 0:
            solutions.append("• Enhancing our response time and quality")
        if metrics.get('ticket_volume_increase', 0) > 0:
            solutions.append("• Implementing proactive monitoring")
        if metrics.get('priority_distribution', {}).get('Critical', 0) > 0:
            solutions.append("• Establishing a dedicated support channel")
        return "\n".join(solutions)

    def _generate_critical_issues(self, metrics):
        """Generate critical issues for escalation emails"""
        issues = []
        if metrics.get('sentiment', 0) < -0.5:
            issues.append("• Significant negative sentiment in recent interactions")
        if metrics.get('ticket_volume_increase', 0) > 0.3:
            issues.append("• Substantial increase in support tickets")
        if metrics.get('priority_distribution', {}).get('Critical', 0) > 0:
            issues.append("• Multiple critical priority issues requiring immediate attention")
        return "\n".join(issues)

    def _generate_status_summary(self, metrics):
        """Generate status summary for check-in emails"""
        summary = []
        if metrics.get('sentiment', 0) < 0:
            summary.append("we've noticed some concerns in recent interactions")
        if metrics.get('ticket_volume_increase', 0) > 0:
            summary.append("there's been an increase in support requests")
        return " and ".join(summary)

    def _generate_feedback_points(self, metrics):
        """Generate feedback points for check-in emails"""
        points = []
        if metrics.get('sentiment', 0) < 0:
            points.append("• Your recent experience with our support team")
        if metrics.get('ticket_volume_increase', 0) > 0:
            points.append("• The effectiveness of our current solutions")
        if metrics.get('priority_distribution', {}).get('Critical', 0) > 0:
            points.append("• Our handling of critical issues")
        return "\n".join(points)
