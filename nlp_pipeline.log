2025-06-02 15:42:33,648 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-02 15:42:33,980 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-02 15:43:13,549 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:43:14,490 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:47:46,030 - INFO - Loading DeepSeek-R1 model: deepseek-ai/DeepSeek-R1-0528
2025-06-02 15:47:46,031 - INFO - Using device: cpu
2025-06-02 15:47:51,639 - INFO - Configuring for CPU-only operation
2025-06-02 15:47:52,779 - WARNING - Pipeline loading failed: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:47:52,780 - INFO - Trying CPU-compatible direct model loading...
2025-06-02 15:47:54,512 - ERROR - Failed to load DeepSeek-R1 model: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:47:54,513 - INFO - Will use fallback LLM system
2025-06-02 15:48:08,069 - INFO - Loading DeepSeek-R1 model: deepseek-ai/DeepSeek-R1-0528
2025-06-02 15:48:08,069 - INFO - Using device: cpu
2025-06-02 15:48:08,070 - INFO - Configuring for CPU-only operation
2025-06-02 15:48:08,815 - WARNING - Pipeline loading failed: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:48:08,815 - INFO - Trying CPU-compatible direct model loading...
2025-06-02 15:48:10,658 - ERROR - Failed to load DeepSeek-R1 model: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:48:10,662 - INFO - Will use fallback LLM system
2025-06-02 15:48:34,563 - INFO - Loading DeepSeek-R1 model: deepseek-ai/DeepSeek-R1-0528
2025-06-02 15:48:34,564 - INFO - Using device: cpu
2025-06-02 15:48:34,564 - INFO - Configuring for CPU-only operation
2025-06-02 15:48:35,198 - WARNING - Pipeline loading failed: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:48:35,199 - INFO - Trying CPU-compatible direct model loading...
2025-06-02 15:48:36,313 - ERROR - Failed to load DeepSeek-R1 model: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:48:36,313 - INFO - Will use fallback LLM system
2025-06-02 15:48:46,149 - INFO - Loading DeepSeek-R1 model: deepseek-ai/DeepSeek-R1-0528
2025-06-02 15:48:46,149 - INFO - Using device: cpu
2025-06-02 15:48:46,150 - INFO - Configuring for CPU-only operation
2025-06-02 15:48:46,864 - WARNING - Pipeline loading failed: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:48:46,865 - INFO - Trying CPU-compatible direct model loading...
2025-06-02 15:48:48,182 - ERROR - Failed to load DeepSeek-R1 model: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:48:48,182 - INFO - Will use fallback LLM system
2025-06-02 15:48:49,127 - INFO - Attempting Google search for: give me an overview of  stefan ribisch
2025-06-02 15:48:49,663 - INFO - Google API response status: 200
2025-06-02 15:48:49,664 - INFO - Google search returned 0 results
2025-06-02 15:48:49,664 - WARNING - No search results found for: give me an overview of  stefan ribisch
2025-06-02 15:48:59,543 - INFO - Loading DeepSeek-R1 model: deepseek-ai/DeepSeek-R1-0528
2025-06-02 15:48:59,544 - INFO - Using device: cpu
2025-06-02 15:48:59,544 - INFO - Configuring for CPU-only operation
2025-06-02 15:49:00,246 - WARNING - Pipeline loading failed: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:49:00,247 - INFO - Trying CPU-compatible direct model loading...
2025-06-02 15:49:01,463 - ERROR - Failed to load DeepSeek-R1 model: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:49:01,463 - INFO - Will use fallback LLM system
2025-06-02 15:49:09,820 - INFO - Loading DeepSeek-R1 model: deepseek-ai/DeepSeek-R1-0528
2025-06-02 15:49:09,820 - INFO - Using device: cpu
2025-06-02 15:49:09,821 - INFO - Configuring for CPU-only operation
2025-06-02 15:49:10,482 - WARNING - Pipeline loading failed: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:49:10,482 - INFO - Trying CPU-compatible direct model loading...
2025-06-02 15:49:11,844 - ERROR - Failed to load DeepSeek-R1 model: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 15:49:11,844 - INFO - Will use fallback LLM system
2025-06-02 15:49:14,353 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:49:15,325 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:49:15,457 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:49:23,764 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 15:49:23,767 - INFO - Loading DeepSeek-R1 model: microsoft/DialoGPT-medium
2025-06-02 15:49:23,768 - INFO - Using device: cpu
2025-06-02 15:49:27,740 - INFO - Configuring for CPU-only operation
2025-06-02 15:49:50,088 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:49:50,313 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 15:49:50,958 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 16:37:53,511 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:37:53,512 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:37:53,512 - INFO - Using device: cpu
2025-06-02 16:37:59,752 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:38:05,355 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:38:05,356 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:38:05,356 - INFO - Using device: cpu
2025-06-02 16:38:07,614 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:38:38,127 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:38:38,128 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:38:38,128 - INFO - Using device: cpu
2025-06-02 16:38:40,852 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:38:43,266 - INFO - Attempting Google search for: give me experience improvement recommendations
2025-06-02 16:38:43,868 - INFO - Google API response status: 200
2025-06-02 16:38:43,869 - INFO - Google search returned 0 results
2025-06-02 16:38:43,869 - WARNING - No search results found for: give me experience improvement recommendations
2025-06-02 16:38:54,293 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:38:54,293 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:38:54,294 - INFO - Using device: cpu
2025-06-02 16:38:56,810 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:39:00,036 - INFO - Attempting Google search for: give me experience improvement recommendations
2025-06-02 16:39:00,403 - INFO - Google API response status: 200
2025-06-02 16:39:00,404 - INFO - Google search returned 0 results
2025-06-02 16:39:00,405 - WARNING - No search results found for: give me experience improvement recommendations
2025-06-02 16:40:20,395 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:40:20,396 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:40:20,396 - INFO - Using device: cpu
2025-06-02 16:40:22,774 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:42:11,514 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:42:11,514 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:42:11,514 - INFO - Using device: cpu
2025-06-02 16:42:13,860 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:42:38,339 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\Ticket_UAT.xls_IAfvnxR.xlsx
2025-06-02 16:42:38,859 - INFO - Successfully loaded 'general_report' sheet
2025-06-02 16:42:38,864 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-06-02 16:42:38,865 - INFO - Found description columns: ['Description', 'Description.1']
2025-06-02 16:42:38,867 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-06-02 16:42:38,872 - INFO - Cleaned Creator column
2025-06-02 16:42:38,877 - INFO - Removed duplicates based on Key column
2025-06-02 16:42:40,828 - INFO - Using 'Description.1' column for text analysis
2025-06-02 16:43:23,593 - INFO - Using 'Created' as creation date
2025-06-02 16:43:23,594 - INFO - Using 'Date of First Response' as response date
2025-06-02 16:43:23,610 - INFO - Using 'Priority' for priority impact calculation
2025-06-02 16:43:23,611 - INFO - Using 'Issue Type' for issue type impact calculation
2025-06-02 16:43:23,678 - INFO - Generated client metrics for 8 creators
2025-06-02 16:43:23,680 - WARNING - No Status column found, using default status distribution
2025-06-02 16:43:23,680 - INFO - Analysis completed successfully
2025-06-02 16:43:43,707 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:43:43,707 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:43:43,708 - INFO - Using device: cpu
2025-06-02 16:43:45,900 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:44:08,005 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:44:08,005 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:44:08,006 - INFO - Using device: cpu
2025-06-02 16:44:10,287 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:44:12,496 - INFO - Attempting Google search for: give me your recommendations
2025-06-02 16:44:13,018 - INFO - Google API response status: 200
2025-06-02 16:44:13,018 - INFO - Google search returned 2 results
2025-06-02 16:44:13,018 - INFO - Successfully formatted Google search result for: give me your recommendations
2025-06-02 16:45:41,476 - WARNING - Not Found: /.well-known/appspecific/com.chrome.devtools.json
2025-06-02 16:45:58,015 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:45:58,015 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:45:58,015 - INFO - Using device: cpu
2025-06-02 16:46:00,290 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:46:33,753 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:46:33,753 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:46:33,754 - INFO - Using device: cpu
2025-06-02 16:46:36,505 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:46:48,075 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:46:48,075 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:46:48,076 - INFO - Using device: cpu
2025-06-02 16:46:50,284 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:47:06,273 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:47:06,273 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:47:06,273 - INFO - Using device: cpu
2025-06-02 16:47:08,585 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:47:59,883 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:47:59,883 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:47:59,883 - INFO - Using device: cpu
2025-06-02 16:48:02,261 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:50:35,659 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:50:35,660 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:50:35,660 - INFO - Using device: cpu
2025-06-02 16:51:10,114 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:51:10,114 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:51:10,114 - INFO - Using device: cpu
2025-06-02 16:51:12,840 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:51:34,831 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:51:34,832 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:51:34,833 - INFO - Using device: cpu
2025-06-02 16:51:37,057 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:51:38,177 - INFO - Attempting Google search for: no it is wrong
2025-06-02 16:51:38,675 - INFO - Google API response status: 200
2025-06-02 16:51:38,676 - INFO - Google search returned 1 results
2025-06-02 16:51:38,676 - INFO - Successfully formatted Google search result for: no it is wrong
2025-06-02 16:52:52,299 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:52:52,299 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:52:52,300 - INFO - Using device: cpu
2025-06-02 16:52:54,444 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 16:52:56,755 - INFO - Attempting Google search for: i want recommendations: actionable improvements
2025-06-02 16:52:57,346 - INFO - Google API response status: 200
2025-06-02 16:52:57,347 - INFO - Google search returned 0 results
2025-06-02 16:52:57,347 - WARNING - No search results found for: i want recommendations: actionable improvements
2025-06-02 16:53:18,814 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:53:18,815 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:53:18,815 - INFO - Using device: cpu
2025-06-02 16:53:21,054 - INFO - Attempting Google search for: implement proactive updates
2025-06-02 16:53:21,558 - INFO - Google API response status: 200
2025-06-02 16:53:21,559 - INFO - Google search returned 0 results
2025-06-02 16:53:21,559 - WARNING - No search results found for: implement proactive updates
2025-06-02 16:53:58,771 - INFO - Using CPU-compatible model: microsoft/DialoGPT-medium
2025-06-02 16:53:58,771 - INFO - Loading model: microsoft/DialoGPT-medium
2025-06-02 16:53:58,771 - INFO - Using device: cpu
2025-06-02 16:54:00,886 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 18:17:44,224 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 18:18:43,562 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-02 18:20:01,593 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-02 18:20:28,924 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-02 18:21:05,661 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-02 18:21:48,961 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-02 18:23:19,411 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\opensource_llm_integration.py changed, reloading.
2025-06-02 18:23:45,596 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\opensource_llm_integration.py changed, reloading.
2025-06-02 18:24:13,796 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\opensource_llm_integration.py changed, reloading.
2025-06-02 18:35:36,833 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 18:35:39,733 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 18:35:41,030 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 18:35:41,031 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 18:36:49,822 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 18:36:51,148 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 18:36:52,175 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 18:36:52,176 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 18:39:58,749 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 18:40:01,953 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 18:40:03,232 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 18:40:03,233 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:19:06,532 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 19:19:08,363 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:19:09,633 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:19:09,634 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:19:23,770 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 19:19:24,641 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:19:25,905 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:19:25,909 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:19:48,078 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 19:19:48,971 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:19:50,254 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:19:50,255 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:19:54,633 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 19:19:54,647 - ERROR - Pipeline generation error: The following `model_kwargs` are not used by the model: ['low_cpu_mem_usage'] (note: typos in the generate arguments will also show up in this list)
2025-06-02 19:20:05,871 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 19:20:07,028 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:20:08,398 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:20:08,398 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:20:40,585 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 19:20:41,801 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:20:42,952 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:20:42,954 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:20:47,084 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 19:20:47,086 - ERROR - Pipeline generation error: The following `model_kwargs` are not used by the model: ['low_cpu_mem_usage'] (note: typos in the generate arguments will also show up in this list)
2025-06-02 19:21:10,605 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 19:21:15,177 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:21:17,407 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 19:21:17,407 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 19:21:22,266 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 19:21:22,268 - ERROR - Pipeline generation error: The following `model_kwargs` are not used by the model: ['low_cpu_mem_usage'] (note: typos in the generate arguments will also show up in this list)
2025-06-02 22:54:02,244 - WARNING - Error while downloading from https://cdn-lfs-us-1.hf.co/repos/9f/12/9f124e23c7197532f1b7eaf621c5fa6b463f2634c18cac12eb28695103048a16/a400155e495b4d6b1cd5b05f04c56032b7dbc6bdff940e046a89ffc1cc2fbdcd?response-content-disposition=inline%3B+filename*%3DUTF-8%27%27pytorch_model.bin%3B+filename%3D%22pytorch_model.bin%22%3B&response-content-type=application%2Foctet-stream&Expires=1748889435&Policy=eyJTdGF0ZW1lbnQiOlt7IkNvbmRpdGlvbiI6eyJEYXRlTGVzc1RoYW4iOnsiQVdTOkVwb2NoVGltZSI6MTc0ODg4OTQzNX19LCJSZXNvdXJjZSI6Imh0dHBzOi8vY2RuLWxmcy11cy0xLmhmLmNvL3JlcG9zLzlmLzEyLzlmMTI0ZTIzYzcxOTc1MzJmMWI3ZWFmNjIxYzVmYTZiNDYzZjI2MzRjMThjYWMxMmViMjg2OTUxMDMwNDhhMTYvYTQwMDE1NWU0OTViNGQ2YjFjZDViMDVmMDRjNTYwMzJiN2RiYzZiZGZmOTQwZTA0NmE4OWZmYzFjYzJmYmRjZD9yZXNwb25zZS1jb250ZW50LWRpc3Bvc2l0aW9uPSomcmVzcG9uc2UtY29udGVudC10eXBlPSoifV19&Signature=HqPS%7ENyZI7Iev8t7QzF3rOKOlfTDk7zZXrmhTp2RiUF1bmvd60rcsf2UDRpqWQf6yFzaUmag5Sf4NQ%7EbQypsJu3AzyJ4PwSWTm%7ErL9V4mr1j3xPX4Z7UhsvT4-hKyESd0fzjvbA73TwRBqoJ%7EkwI4lsFJIIz02Iwd%7ERwujiJh-BBSKUwZZ7oXchiQ%7ErFvXGQ9tF8BdEo2tVoEUGwEbeyp9q3jrZybZOv53GIb6ygdrC58I1NKA5TRVtwBlQluLJ95ne7wlrVJXpWp3KejOfa29KqSaJ9hEl5gr-1RT9X3G3huGbuT85wG4jbdqJBvaG3txGVuYummy-uWjts%7ENl1CQ__&Key-Pair-Id=K24J24Z295AEI9: HTTPSConnectionPool(host='cdn-lfs-us-1.hf.co', port=443): Read timed out.
Trying to resume download...
2025-06-02 22:54:14,004 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 22:54:14,014 - ERROR - Pipeline generation error: The following `model_kwargs` are not used by the model: ['low_cpu_mem_usage'] (note: typos in the generate arguments will also show up in this list)
2025-06-02 22:57:16,213 - INFO - Attempting to load: DeepSeek R1 (cached)
2025-06-02 22:57:17,592 - WARNING - Pipeline loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 22:57:18,739 - WARNING - Direct loading failed for deepseek-ai/DeepSeek-R1-0528: No GPU found. A GPU is needed for FP8 quantization.
2025-06-02 22:57:18,739 - INFO - Attempting to load: DeepSeek Coder 1.3B
2025-06-02 22:57:21,925 - INFO - Using DeepSeek-R1 for Jira query
2025-06-02 22:57:21,932 - ERROR - Pipeline generation error: The following `model_kwargs` are not used by the model: ['low_cpu_mem_usage'] (note: typos in the generate arguments will also show up in this list)
2025-06-02 22:57:21,935 - INFO - Attempting Google search for: compare with previous month
2025-06-02 22:57:23,798 - INFO - Google API response status: 200
2025-06-02 22:57:23,799 - INFO - Google search returned 0 results
2025-06-02 22:57:23,799 - WARNING - No search results found for: compare with previous month
2025-06-02 23:12:13,327 - INFO - Ollama service available with 1 models
2025-06-02 23:12:13,328 - INFO - Available Ollama models: ['deepseek-r1:1.5b']
2025-06-02 23:12:13,328 - INFO - No preferred models found, attempting to pull deepseek-coder:1.3b
2025-06-02 23:12:13,329 - INFO - Pulling Ollama model: deepseek-coder:1.3b
2025-06-02 23:13:11,799 - INFO - Ollama service available with 1 models
2025-06-02 23:13:11,800 - INFO - Available Ollama models: ['deepseek-r1:1.5b']
2025-06-02 23:13:11,801 - INFO - No preferred models found, attempting to pull deepseek-coder:1.3b
2025-06-02 23:13:11,801 - INFO - Pulling Ollama model: deepseek-coder:1.3b
2025-06-02 23:13:40,170 - INFO - Ollama service available with 1 models
2025-06-02 23:13:40,170 - INFO - Available Ollama models: ['deepseek-r1:1.5b']
2025-06-02 23:13:40,171 - INFO - No preferred models found, attempting to pull deepseek-coder:1.3b
2025-06-02 23:13:40,171 - INFO - Pulling Ollama model: deepseek-coder:1.3b
2025-06-02 23:16:05,820 - INFO - Ollama service available with 1 models
2025-06-02 23:16:05,820 - INFO - Available Ollama models: ['deepseek-r1:1.5b']
2025-06-02 23:16:05,821 - INFO - No preferred models found, attempting to pull deepseek-coder:1.3b
2025-06-02 23:16:05,821 - INFO - Pulling Ollama model: deepseek-coder:1.3b
2025-06-02 23:27:59,233 - INFO - Successfully pulled model: deepseek-coder:1.3b
2025-06-02 23:27:59,234 - INFO - Successfully pulled model: deepseek-coder:1.3b
2025-06-02 23:27:59,236 - INFO - Successfully pulled model: deepseek-coder:1.3b
2025-06-02 23:27:59,237 - INFO - Successfully pulled model: deepseek-coder:1.3b
2025-06-02 23:27:59,365 - INFO - Using Ollama DeepSeek for Jira query
2025-06-02 23:27:59,404 - INFO - Using Ollama DeepSeek for Jira query
2025-06-02 23:27:59,416 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-02 23:27:59,417 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-02 23:28:03,697 - INFO - Ollama API response received (380 chars)
2025-06-02 23:28:04,236 - INFO - Ollama API response received (618 chars)
2025-06-02 23:28:18,958 - INFO - Ollama service available with 2 models
2025-06-02 23:28:18,958 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:28:36,135 - INFO - Ollama service available with 2 models
2025-06-02 23:28:36,135 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:28:36,151 - INFO - Using Ollama DeepSeek for Jira query
2025-06-02 23:28:36,152 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-02 23:28:39,237 - INFO - Ollama API response received (436 chars)
2025-06-02 23:28:39,238 - INFO - Attempting Google search for: give me an overview of  stefan ribisch
2025-06-02 23:28:39,654 - INFO - Google API response status: 200
2025-06-02 23:28:39,654 - INFO - Google search returned 0 results
2025-06-02 23:28:39,654 - WARNING - No search results found for: give me an overview of  stefan ribisch
2025-06-02 23:29:13,060 - INFO - Ollama service available with 2 models
2025-06-02 23:29:13,061 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:29:13,078 - INFO - Using Ollama DeepSeek for Jira query
2025-06-02 23:29:13,078 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-02 23:29:16,145 - INFO - Ollama API response received (367 chars)
2025-06-02 23:30:04,980 - INFO - Ollama service available with 2 models
2025-06-02 23:30:04,980 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:30:13,124 - INFO - Ollama service available with 2 models
2025-06-02 23:30:13,124 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:30:13,147 - INFO - Using Ollama DeepSeek for Jira query
2025-06-02 23:30:13,147 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-02 23:30:15,940 - INFO - Ollama API response received (230 chars)
2025-06-02 23:30:35,535 - INFO - Ollama service available with 2 models
2025-06-02 23:30:35,536 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:30:55,725 - INFO - Ollama service available with 2 models
2025-06-02 23:30:55,725 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:30:55,741 - INFO - Using Ollama DeepSeek for Jira query
2025-06-02 23:30:55,741 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-02 23:30:58,695 - INFO - Ollama API response received (321 chars)
2025-06-02 23:32:10,240 - INFO - Ollama service available with 2 models
2025-06-02 23:32:10,241 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:32:10,257 - INFO - Using Ollama DeepSeek for Jira query
2025-06-02 23:32:10,258 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-02 23:32:13,222 - INFO - Ollama API response received (305 chars)
2025-06-02 23:32:13,223 - INFO - Attempting Google search for: latest jira best practices
2025-06-02 23:32:14,147 - INFO - Google API response status: 200
2025-06-02 23:32:14,147 - INFO - Google search returned 0 results
2025-06-02 23:32:14,148 - WARNING - No search results found for: latest jira best practices
2025-06-02 23:33:00,105 - INFO - Ollama service available with 2 models
2025-06-02 23:33:00,105 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:40:18,306 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-02 23:40:54,350 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-02 23:41:36,939 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-02 23:42:06,177 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-02 23:43:36,714 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\database_integration.py changed, reloading.
2025-06-02 23:43:55,493 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\database_integration.py changed, reloading.
2025-06-02 23:44:18,567 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\database_integration.py changed, reloading.
2025-06-02 23:44:37,456 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\database_integration.py changed, reloading.
2025-06-02 23:45:05,135 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\database_integration.py changed, reloading.
2025-06-02 23:45:26,925 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\database_integration.py changed, reloading.
2025-06-02 23:45:48,907 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\database_integration.py changed, reloading.
2025-06-02 23:46:11,870 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\database_integration.py changed, reloading.
2025-06-02 23:46:38,987 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\database_integration.py changed, reloading.
2025-06-02 23:47:58,060 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\database_integration.py changed, reloading.
2025-06-02 23:48:18,796 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\query_processor.py changed, reloading.
2025-06-02 23:48:35,790 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-02 23:50:30,045 - INFO - Ollama service available with 2 models
2025-06-02 23:50:30,046 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:50:30,137 - INFO - Retrieved comprehensive data for user Hamada
2025-06-02 23:50:30,138 - INFO - Enhanced context data gathered successfully
2025-06-02 23:50:39,286 - INFO - Ollama service available with 2 models
2025-06-02 23:50:39,287 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:50:39,394 - INFO - Processing as database query
2025-06-02 23:50:39,398 - INFO - Processing client_queries query: Which clients need immediate attention?
2025-06-02 23:50:39,434 - INFO - Retrieved comprehensive data for user Hamada
2025-06-02 23:51:52,093 - INFO - Ollama service available with 2 models
2025-06-02 23:51:52,093 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:51:52,139 - INFO - Retrieved comprehensive data for user Hamada
2025-06-02 23:51:52,141 - INFO - Enhanced context data gathered successfully
2025-06-02 23:51:52,141 - INFO - Using Ollama DeepSeek for Jira query
2025-06-02 23:51:52,141 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-02 23:51:55,154 - INFO - Ollama API response received (359 chars)
2025-06-02 23:52:31,679 - INFO - Ollama service available with 2 models
2025-06-02 23:52:31,680 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:52:31,713 - INFO - Processing as database query
2025-06-02 23:52:31,714 - INFO - Processing client_queries query: Show client metrics
2025-06-02 23:52:31,737 - INFO - Retrieved comprehensive data for user Hamada
2025-06-02 23:52:38,098 - INFO - Ollama service available with 2 models
2025-06-02 23:52:38,100 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:52:38,156 - INFO - Processing as database query
2025-06-02 23:52:38,161 - INFO - Processing temporal_queries query: Identify trends
2025-06-02 23:52:38,188 - INFO - Retrieved comprehensive data for user Hamada
2025-06-02 23:52:53,503 - INFO - Ollama service available with 2 models
2025-06-02 23:52:53,504 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:52:53,531 - INFO - Processing as database query
2025-06-02 23:52:53,533 - INFO - Processing temporal_queries query: Compare with previous month
2025-06-02 23:52:53,556 - INFO - Retrieved comprehensive data for user Hamada
2025-06-02 23:53:11,833 - INFO - Ollama service available with 2 models
2025-06-02 23:53:11,833 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:53:11,879 - INFO - Retrieved comprehensive data for user Hamada
2025-06-02 23:53:11,880 - INFO - Enhanced context data gathered successfully
2025-06-02 23:53:11,880 - INFO - Using Ollama DeepSeek for Jira query
2025-06-02 23:53:11,880 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-02 23:53:14,812 - INFO - Ollama API response received (306 chars)
2025-06-02 23:53:14,813 - INFO - Attempting Google search for: give me an overview of  stefan ribisch
2025-06-02 23:53:15,393 - INFO - Google API response status: 200
2025-06-02 23:53:15,393 - INFO - Google search returned 0 results
2025-06-02 23:53:15,393 - WARNING - No search results found for: give me an overview of  stefan ribisch
2025-06-02 23:53:43,966 - INFO - Ollama service available with 2 models
2025-06-02 23:53:43,966 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:53:44,046 - INFO - Retrieved comprehensive data for user Hamada
2025-06-02 23:53:44,047 - INFO - Enhanced context data gathered successfully
2025-06-02 23:53:44,047 - INFO - Using Ollama DeepSeek for Jira query
2025-06-02 23:53:44,047 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-02 23:53:47,121 - INFO - Ollama API response received (380 chars)
2025-06-02 23:54:40,101 - INFO - Ollama service available with 2 models
2025-06-02 23:54:40,101 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:54:40,130 - INFO - Retrieved comprehensive data for user Hamada
2025-06-02 23:54:40,130 - INFO - Enhanced context data gathered successfully
2025-06-02 23:54:47,558 - INFO - Ollama service available with 2 models
2025-06-02 23:54:47,559 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:54:47,606 - INFO - Retrieved comprehensive data for user Hamada
2025-06-02 23:54:47,606 - INFO - Enhanced context data gathered successfully
2025-06-02 23:54:59,420 - INFO - Ollama service available with 2 models
2025-06-02 23:54:59,421 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-02 23:54:59,478 - INFO - Retrieved comprehensive data for user Hamada
2025-06-02 23:54:59,479 - INFO - Enhanced context data gathered successfully
2025-06-02 23:54:59,479 - INFO - Using Ollama DeepSeek for Jira query
2025-06-02 23:54:59,480 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-02 23:55:02,721 - INFO - Ollama API response received (495 chars)
2025-06-02 23:55:02,722 - INFO - Attempting Google search for: give me an overview of  stefan ribisch
2025-06-02 23:55:03,033 - INFO - Google API response status: 200
2025-06-02 23:55:03,033 - INFO - Google search returned 0 results
2025-06-02 23:55:03,033 - WARNING - No search results found for: give me an overview of  stefan ribisch
2025-06-02 23:56:37,674 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\tickets_prod.xls_9YKjC8n.xlsx
2025-06-02 23:56:38,118 - INFO - Successfully loaded 'general_report' sheet
2025-06-02 23:56:38,123 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-06-02 23:56:38,123 - INFO - Found description columns: ['Description', 'Description.1']
2025-06-02 23:56:38,125 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-06-02 23:56:38,127 - INFO - Cleaned Creator column
2025-06-02 23:56:38,131 - INFO - Removed duplicates based on Key column
2025-06-02 23:56:45,968 - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-06-03 00:04:40,899 - INFO - Processing file: C:\Users\<USER>\Desktop\APP D\media\jira_files\tickets_prod.xls_fmJ0bg9.xlsx
2025-06-03 00:04:40,983 - INFO - Successfully loaded 'general_report' sheet
2025-06-03 00:04:40,984 - INFO - Columns before processing: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description', 'Description.1']
2025-06-03 00:04:40,985 - INFO - Found description columns: ['Description', 'Description.1']
2025-06-03 00:04:40,986 - INFO - Dropped first Description column, remaining columns: ['Issue Type', 'Key', 'Priority', 'Summary', 'Creator', 'Created', 'Date of First Response', 'Redeclared', 'Description.1']
2025-06-03 00:04:40,988 - INFO - Cleaned Creator column
2025-06-03 00:04:40,989 - INFO - Removed duplicates based on Key column
2025-06-03 00:05:13,863 - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-06-03 00:05:14,648 - INFO - Using 'Description.1' column for text analysis
2025-06-03 00:05:16,943 - WARNING - Xet Storage is enabled for this repo, but the 'hf_xet' package is not installed. Falling back to regular HTTP download. For better performance, install the package with: `pip install huggingface_hub[hf_xet]` or `pip install hf_xet`
2025-06-03 00:06:01,996 - INFO - Using 'Created' as creation date
2025-06-03 00:06:01,997 - INFO - Using 'Date of First Response' as response date
2025-06-03 00:06:02,005 - INFO - Using 'Priority' for priority impact calculation
2025-06-03 00:06:02,006 - INFO - Using 'Issue Type' for issue type impact calculation
2025-06-03 00:06:02,059 - INFO - Generated client metrics for 8 creators
2025-06-03 00:06:02,061 - WARNING - No Status column found, using default status distribution
2025-06-03 00:06:02,061 - INFO - Analysis completed successfully
2025-06-03 00:20:10,932 - INFO - Using 'Description.1' column for text analysis
2025-06-03 00:20:55,107 - INFO - Using 'Created' as creation date
2025-06-03 00:20:55,107 - INFO - Using 'Date of First Response' as response date
2025-06-03 00:20:55,111 - INFO - Using 'Priority' for priority impact calculation
2025-06-03 00:20:55,112 - INFO - Using 'Issue Type' for issue type impact calculation
2025-06-03 00:20:55,152 - INFO - Generated client metrics for 8 creators
2025-06-03 00:20:55,155 - WARNING - No Status column found, using default status distribution
2025-06-03 00:20:55,156 - INFO - Analysis completed successfully
2025-06-03 00:52:45,414 - INFO - Ollama service available with 2 models
2025-06-03 00:52:45,415 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 00:52:45,493 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 00:52:45,493 - INFO - Enhanced context data gathered successfully
2025-06-03 00:52:45,494 - INFO - Using Ollama DeepSeek for Jira query
2025-06-03 00:52:45,494 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-03 00:52:49,196 - INFO - Ollama API response received (261 chars)
2025-06-03 00:53:13,035 - INFO - Ollama service available with 2 models
2025-06-03 00:53:13,035 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 00:53:13,061 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 00:53:13,061 - INFO - Enhanced context data gathered successfully
2025-06-03 00:53:13,061 - INFO - Using Ollama DeepSeek for Jira query
2025-06-03 00:53:13,061 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-03 00:53:16,243 - INFO - Ollama API response received (475 chars)
2025-06-03 00:53:16,243 - INFO - Attempting Google search for: 
2025-06-03 00:53:16,592 - INFO - Google API response status: 400
2025-06-03 00:53:16,592 - ERROR - Google API error 400: {'error': {'code': 400, 'message': 'Request contains an invalid argument.', 'errors': [{'message': 'Request contains an invalid argument.', 'domain': 'global', 'reason': 'badRequest'}], 'status': 'INVALID_ARGUMENT'}}
2025-06-03 00:53:16,592 - ERROR - Invalid search parameters
2025-06-03 00:53:17,254 - ERROR - DuckDuckGo search failed: Expecting value: line 1 column 1 (char 0)
2025-06-03 00:53:25,655 - INFO - Ollama service available with 2 models
2025-06-03 00:53:25,655 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 00:53:25,670 - INFO - Processing as database query
2025-06-03 00:53:25,670 - INFO - Processing client_queries query: Show client metrics
2025-06-03 00:53:25,686 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 00:53:40,621 - INFO - Ollama service available with 2 models
2025-06-03 00:53:40,621 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 00:53:40,652 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 00:53:40,652 - INFO - Enhanced context data gathered successfully
2025-06-03 00:53:40,653 - INFO - Using Ollama DeepSeek for Jira query
2025-06-03 00:53:40,653 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-03 00:53:43,631 - INFO - Ollama API response received (287 chars)
2025-06-03 00:56:44,618 - INFO - Ollama service available with 2 models
2025-06-03 00:56:44,619 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 00:56:44,629 - INFO - Processing as database query
2025-06-03 00:56:44,629 - INFO - Processing client_queries query: Show client metrics
2025-06-03 00:56:44,644 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 00:56:48,864 - INFO - Ollama service available with 2 models
2025-06-03 00:56:48,864 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 00:56:48,875 - INFO - Processing as database query
2025-06-03 00:56:48,875 - INFO - Processing temporal_queries query: Identify trends
2025-06-03 00:56:48,890 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 00:57:03,217 - INFO - Ollama service available with 2 models
2025-06-03 00:57:03,218 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 00:57:03,229 - INFO - Processing as database query
2025-06-03 00:57:03,229 - INFO - Processing temporal_queries query: Compare with previous month
2025-06-03 00:57:03,243 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 00:57:19,250 - INFO - Ollama service available with 2 models
2025-06-03 00:57:19,250 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 00:57:19,285 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 00:57:19,286 - INFO - Enhanced context data gathered successfully
2025-06-03 00:57:19,286 - INFO - Attempting Google search for: the next step
2025-06-03 00:57:19,789 - INFO - Google API response status: 200
2025-06-03 00:57:19,789 - INFO - Google search returned 2 results
2025-06-03 00:57:19,789 - INFO - Successfully formatted Google search result for: the next step
2025-06-03 00:59:07,327 - INFO - Ollama service available with 2 models
2025-06-03 00:59:07,327 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 00:59:07,359 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 00:59:07,359 - INFO - Enhanced context data gathered successfully
2025-06-03 00:59:07,359 - INFO - Using Ollama DeepSeek for Jira query
2025-06-03 00:59:07,360 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-03 00:59:11,175 - INFO - Ollama API response received (477 chars)
2025-06-03 00:59:48,285 - INFO - Ollama service available with 2 models
2025-06-03 00:59:48,285 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 00:59:48,317 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 00:59:48,317 - INFO - Enhanced context data gathered successfully
2025-06-03 00:59:48,317 - INFO - Using Ollama DeepSeek for Jira query
2025-06-03 00:59:48,317 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-03 00:59:51,518 - INFO - Ollama API response received (455 chars)
2025-06-03 00:59:51,520 - INFO - Attempting Google search for: complet your idea please
2025-06-03 00:59:52,116 - INFO - Google API response status: 200
2025-06-03 00:59:52,117 - INFO - Google search returned 0 results
2025-06-03 00:59:52,117 - WARNING - No search results found for: complet your idea please
2025-06-03 01:00:56,303 - INFO - Ollama service available with 2 models
2025-06-03 01:00:56,303 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 01:00:56,346 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 01:00:56,348 - INFO - Enhanced context data gathered successfully
2025-06-03 01:00:56,348 - INFO - Using Ollama DeepSeek for Jira query
2025-06-03 01:00:56,348 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-03 01:00:59,089 - INFO - Ollama API response received (201 chars)
2025-06-03 01:01:09,405 - INFO - Ollama service available with 2 models
2025-06-03 01:01:09,408 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 01:01:09,460 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 01:01:09,460 - INFO - Enhanced context data gathered successfully
2025-06-03 01:01:09,460 - INFO - Using Ollama DeepSeek for Jira query
2025-06-03 01:01:09,461 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-03 01:01:12,709 - INFO - Ollama API response received (453 chars)
2025-06-03 01:01:12,709 - INFO - Attempting Google search for: give me an overview of  stefan ribisch
2025-06-03 01:01:13,030 - INFO - Google API response status: 200
2025-06-03 01:01:13,031 - INFO - Google search returned 0 results
2025-06-03 01:01:13,031 - WARNING - No search results found for: give me an overview of  stefan ribisch
2025-06-03 18:57:14,654 - INFO - Ollama service available with 2 models
2025-06-03 18:57:14,655 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 18:57:14,851 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 18:57:14,851 - INFO - Enhanced context data gathered successfully
2025-06-03 18:57:19,801 - INFO - Ollama service available with 2 models
2025-06-03 18:57:19,802 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 18:57:19,903 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 18:57:19,904 - INFO - Enhanced context data gathered successfully
2025-06-03 18:57:19,904 - INFO - Using Ollama DeepSeek for Jira query
2025-06-03 18:57:19,904 - INFO - Calling Ollama API with model: deepseek-coder:1.3b
2025-06-03 18:57:26,362 - INFO - Ollama API response received (445 chars)
2025-06-03 18:58:44,089 - INFO - Ollama service available with 2 models
2025-06-03 18:58:44,089 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 18:58:44,140 - INFO - Processing as database query
2025-06-03 18:58:44,141 - INFO - Processing temporal_queries query: Compare with previous month
2025-06-03 18:58:44,168 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 19:03:14,648 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 19:04:02,192 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_handler.py changed, reloading.
2025-06-03 19:05:32,526 - INFO - Ollama service available with 2 models
2025-06-03 19:05:32,528 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 19:05:32,696 - ERROR - Error gathering context data: Cannot resolve keyword 'user' into field. Choices are: actionable_insights, chatmessage, client_metrics, common_themes, created_at, id, issue_count, jira_file, jira_file_id, priority_distribution, sentiment_analysis, status_distribution, theme_visualization, ticket_types
2025-06-03 19:05:42,304 - INFO - Ollama service available with 2 models
2025-06-03 19:05:42,305 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 19:05:42,361 - INFO - Processing as database query
2025-06-03 19:05:42,364 - INFO - Processing statistical_queries query: Show latest metrics
2025-06-03 19:05:42,459 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 19:06:05,508 - INFO - Ollama service available with 2 models
2025-06-03 19:06:05,509 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 19:06:05,580 - INFO - Processing as database query
2025-06-03 19:06:05,581 - INFO - Processing client_queries query: Show client metrics
2025-06-03 19:06:05,633 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 19:06:18,375 - INFO - Ollama service available with 2 models
2025-06-03 19:06:18,376 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 19:06:18,456 - INFO - Processing as database query
2025-06-03 19:06:18,456 - INFO - Processing temporal_queries query: Client satisfaction trends
2025-06-03 19:06:18,471 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 19:06:34,233 - INFO - Ollama service available with 2 models
2025-06-03 19:06:34,234 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 19:06:34,313 - INFO - Processing as database query
2025-06-03 19:06:34,314 - INFO - Processing statistical_queries query: Team performance metrics
2025-06-03 19:06:34,406 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 19:06:44,987 - INFO - Ollama service available with 2 models
2025-06-03 19:06:44,988 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 19:06:45,019 - ERROR - Error gathering context data: Cannot resolve keyword 'user' into field. Choices are: actionable_insights, chatmessage, client_metrics, common_themes, created_at, id, issue_count, jira_file, jira_file_id, priority_distribution, sentiment_analysis, status_distribution, theme_visualization, ticket_types
2025-06-03 19:06:45,020 - INFO - Attempting Google search for: process improvement suggestions
2025-06-03 19:06:45,697 - INFO - Google API response status: 200
2025-06-03 19:06:45,697 - INFO - Google search returned 0 results
2025-06-03 19:06:45,697 - WARNING - No search results found for: process improvement suggestions
2025-06-03 19:07:19,949 - INFO - Ollama service available with 2 models
2025-06-03 19:07:19,949 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 19:07:19,975 - ERROR - Error gathering context data: Cannot resolve keyword 'user' into field. Choices are: actionable_insights, chatmessage, client_metrics, common_themes, created_at, id, issue_count, jira_file, jira_file_id, priority_distribution, sentiment_analysis, status_distribution, theme_visualization, ticket_types
2025-06-03 19:07:19,977 - ERROR - Error in Jira query handling: 'IntelligentAgent' object has no attribute '_get_llm_response'
2025-06-03 19:07:25,665 - INFO - Ollama service available with 2 models
2025-06-03 19:07:25,666 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 19:07:25,715 - ERROR - Error gathering context data: Cannot resolve keyword 'user' into field. Choices are: actionable_insights, chatmessage, client_metrics, common_themes, created_at, id, issue_count, jira_file, jira_file_id, priority_distribution, sentiment_analysis, status_distribution, theme_visualization, ticket_types
2025-06-03 19:07:25,716 - INFO - Attempting Google search for: identify bottlenecks
2025-06-03 19:07:26,289 - INFO - Google API response status: 200
2025-06-03 19:07:26,289 - INFO - Google search returned 0 results
2025-06-03 19:07:26,289 - WARNING - No search results found for: identify bottlenecks
2025-06-03 19:07:33,844 - INFO - Ollama service available with 2 models
2025-06-03 19:07:33,844 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 19:07:33,880 - INFO - Processing as database query
2025-06-03 19:07:33,881 - INFO - Processing general query: Resource allocation insights
2025-06-03 19:07:33,983 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 19:07:49,088 - INFO - Ollama service available with 2 models
2025-06-03 19:07:49,088 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 19:07:49,127 - INFO - Processing as database query
2025-06-03 19:07:49,127 - INFO - Processing priority_queries query: What are my critical issues?
2025-06-03 19:07:49,153 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 19:08:04,142 - INFO - Ollama service available with 2 models
2025-06-03 19:08:04,142 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 19:08:04,270 - INFO - Processing as database query
2025-06-03 19:08:04,271 - INFO - Processing general query: Resource allocation insights
2025-06-03 19:08:04,302 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 19:08:19,621 - INFO - Ollama service available with 2 models
2025-06-03 19:08:19,622 - INFO - Available Ollama models: ['deepseek-coder:1.3b', 'deepseek-r1:1.5b']
2025-06-03 19:08:19,657 - INFO - Processing as database query
2025-06-03 19:08:19,658 - INFO - Processing performance_queries query: 'How can I improve performance?
2025-06-03 19:08:19,679 - INFO - Retrieved comprehensive data for user Hamada
2025-06-03 19:13:53,863 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-03 19:14:46,816 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 19:20:36,171 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 19:25:55,336 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 19:25:55,808 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 19:26:07,979 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 19:26:08,654 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 22:27:31,946 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\__init__.py changed, reloading.
2025-06-03 22:27:41,119 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 22:33:15,350 - WARNING - Not Found: /analyzer/chat_message/
2025-06-03 22:33:21,447 - WARNING - Not Found: /analyzer/chat_message/
2025-06-03 22:33:29,609 - WARNING - Not Found: /analyzer/chat_message/
2025-06-03 22:33:55,304 - WARNING - Not Found: /analyzer/chat_message/
2025-06-03 22:34:25,898 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_views.py changed, reloading.
2025-06-03 22:35:10,574 - WARNING - Not Found: /analyzer/api/chat/
2025-06-03 22:35:37,978 - WARNING - Not Found: /analyzer/api/chat/
2025-06-03 22:36:23,708 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_views.py changed, reloading.
2025-06-03 22:36:42,402 - WARNING - Not Found: /analyzer/api/chat/
2025-06-03 22:40:33,395 - WARNING - Not Found: /analyzer/ai-agent/
2025-06-03 22:40:33,469 - WARNING - Not Found: /favicon.ico
2025-06-03 22:41:38,609 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\database_integration.py changed, reloading.
2025-06-03 22:42:03,069 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\database_integration.py changed, reloading.
2025-06-03 22:42:31,127 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-03 22:43:35,408 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\deepseek_llm_handler.py changed, reloading.
2025-06-03 22:43:53,927 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 22:44:35,317 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 22:45:08,066 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 22:45:37,730 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\chat_views.py changed, reloading.
2025-06-03 22:46:50,652 - WARNING - Not Found: /analyzer/
2025-06-03 22:48:54,440 - WARNING - Not Found: /analyzer/api/chat/
2025-06-03 22:49:40,365 - WARNING - Not Found: /analyzer/api/chat/
2025-06-03 22:50:28,660 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\database_integration.py changed, reloading.
2025-06-03 22:51:09,998 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 22:54:38,441 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 22:55:02,703 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 22:55:47,286 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 22:56:08,539 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 22:56:39,340 - INFO - C:\Users\<USER>\Desktop\APP D\analyzer\intelligent_agent.py changed, reloading.
2025-06-03 22:58:36,502 - WARNING - Not Found: /analyzer/api/chat/
2025-06-03 22:58:50,648 - WARNING - Not Found: /analyzer/api/chat/
2025-06-03 22:58:59,296 - WARNING - Not Found: /analyzer/api/chat/
2025-06-03 22:59:50,702 - WARNING - Bad Request: /api/chat/
2025-06-03 22:59:50,704 - ERROR - Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
2025-06-03 22:59:50,716 - WARNING - Bad Request: /api/chat/
2025-06-03 22:59:50,718 - ERROR - Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
2025-06-03 22:59:50,728 - WARNING - Bad Request: /api/chat/
2025-06-03 22:59:50,731 - ERROR - Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
2025-06-03 22:59:50,741 - WARNING - Bad Request: /api/chat/
2025-06-03 22:59:50,743 - ERROR - Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
2025-06-03 22:59:50,753 - WARNING - Bad Request: /api/chat/
2025-06-03 22:59:50,776 - ERROR - Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
2025-06-03 22:59:50,787 - WARNING - Bad Request: /ai-agent/
2025-06-03 22:59:50,788 - ERROR - Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
2025-06-03 22:59:50,798 - WARNING - Bad Request: /
2025-06-03 22:59:50,799 - ERROR - Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
2025-06-03 22:59:50,809 - WARNING - Bad Request: /overview/
2025-06-03 22:59:50,810 - ERROR - Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "c:\Users\<USER>\Desktop\APP D\.venv\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
2025-06-03 22:59:50,820 - WARNING - Bad Request: /dashboard/
