from django.urls import path
from django.views.generic import TemplateView
from . import views
from . import chat_views

urlpatterns = [
    path('', views.home, name='home'),
    path('overview/', views.overview, name='overview'),
    path('dashboard/', views.dashboard, name='dashboard'),
    path('upload/', views.upload_file, name='upload_file'),
    path('process/<int:file_id>/', views.process_file, name='process_file'),
    path('analysis/<int:analysis_id>/', views.view_analysis, name='view_analysis'),
    path('debug-analysis/<int:analysis_id>/', views.debug_analysis, name='debug_analysis'),
    path('download-data/<int:analysis_id>/', views.download_cleaned_data, name='download_cleaned_data'),
    path('regenerate/<int:file_id>/', views.regenerate_analysis, name='regenerate_analysis'),
    path('delete/<int:file_id>/', views.delete_file, name='delete_file'),
    path('ai-agent/', views.ai_agent, name='ai_agent'),
    path('api/chat/', chat_views.chat_message, name='chat_message'),
    path('client-overview/', views.client_overview, name='client_overview'),
    path('client-overview/<str:client_name>/', views.client_detail, name='client_detail'),
    path('team-overview/', views.team_overview, name='team_overview'),
    path('test-logo/', TemplateView.as_view(template_name='test_logo.html'), name='test_logo'),
]
