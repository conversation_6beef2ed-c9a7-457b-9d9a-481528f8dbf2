"""
Natural Language Query Processor for AI Agent
Translates user questions into database queries and insights
"""
import logging
import re
from datetime import datetime, timedelta
from .database_integration import DatabaseIntegrator
from .models import AnalysisResult, JiraFile

logger = logging.getLogger(__name__)

class QueryProcessor:
    """
    Processes natural language queries and provides data-driven responses
    """
    
    def __init__(self, user):
        self.user = user
        self.db_integrator = DatabaseIntegrator(user)
        
        # Query pattern mappings
        self.query_patterns = {
            'client_queries': [
                r'(top|best|worst|highest|lowest)\s+(clients?|customers?)',
                r'(clients?|customers?)\s+(with|having)\s+(most|least|highest|lowest)',
                r'(show|list|display)\s+(clients?|customers?)',
                r'(which|what)\s+(clients?|customers?)',
                r'client\s+(analysis|overview|summary|metrics)',
                r'customer\s+(satisfaction|experience|sentiment)'
            ],
            'temporal_queries': [
                r'(over\s+time|timeline|trend|change|evolution)',
                r'(last|past|recent)\s+(month|week|year|days?)',
                r'(compare|comparison)\s+(with|to)\s+(previous|last|earlier)',
                r'(how\s+has|how\s+have)\s+.+\s+(changed|evolved|improved|declined)',
                r'(historical|history|past)\s+(data|analysis|trends?)',
                r'(before|after|since|until)'
            ],
            'priority_queries': [
                r'(priority|priorities|urgent|critical|high|low)',
                r'(most|least)\s+(important|urgent|critical)',
                r'(distribution|breakdown)\s+(of|by)\s+(priority|priorities)',
                r'(critical|urgent|high|medium|low)\s+(issues?|tickets?)'
            ],
            'sentiment_queries': [
                r'(sentiment|feeling|satisfaction|experience|mood)',
                r'(positive|negative|neutral)\s+(feedback|sentiment|experience)',
                r'(happy|unhappy|satisfied|dissatisfied|frustrated)',
                r'(customer\s+experience|client\s+satisfaction)',
                r'(sentiment\s+analysis|emotional\s+analysis)'
            ],
            'file_queries': [
                r'(files?|uploads?|analyses?|datasets?)',
                r'(which\s+file|what\s+file|file\s+with)',
                r'(latest|newest|oldest|first|last)\s+(file|analysis|upload)',
                r'(compare|comparison)\s+(files?|analyses?)',
                r'(file\s+summary|analysis\s+summary)'
            ],
            'statistical_queries': [
                r'(total|sum|count|number|amount)',
                r'(average|mean|median|typical)',
                r'(statistics|stats|metrics|numbers)',
                r'(how\s+many|how\s+much|what\s+is\s+the)',
                r'(percentage|percent|ratio|proportion)'
            ],
            'performance_queries': [
                r'(performance|efficiency|quality|effectiveness)',
                r'(improve|optimization|better|enhance)',
                r'(recommendations|suggestions|advice|tips)',
                r'(best\s+practices|what\s+should|how\s+to)',
                r'(problems|issues|concerns|challenges)'
            ]
        }
    
    async def process_query(self, query):
        """
        Process a natural language query and return relevant insights
        """
        try:
            query_lower = query.lower()
            query_type = self._classify_query(query_lower)
            
            logger.info(f"Processing {query_type} query: {query}")
            
            # Get comprehensive data
            comprehensive_data = await self.db_integrator.get_comprehensive_user_data()
            
            if not comprehensive_data:
                return "I don't have any analysis data available. Please upload and analyze some Jira files first."
            
            # Route to appropriate handler
            if query_type == 'client_queries':
                return self._handle_client_query(query_lower, comprehensive_data)
            elif query_type == 'temporal_queries':
                return self._handle_temporal_query(query_lower, comprehensive_data)
            elif query_type == 'priority_queries':
                return self._handle_priority_query(query_lower, comprehensive_data)
            elif query_type == 'sentiment_queries':
                return self._handle_sentiment_query(query_lower, comprehensive_data)
            elif query_type == 'file_queries':
                return self._handle_file_query(query_lower, comprehensive_data)
            elif query_type == 'statistical_queries':
                return self._handle_statistical_query(query_lower, comprehensive_data)
            elif query_type == 'performance_queries':
                return self._handle_performance_query(query_lower, comprehensive_data)
            else:
                return self._handle_general_query(query_lower, comprehensive_data)
                
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return f"I encountered an error while processing your query: {str(e)}"
    
    def _classify_query(self, query):
        """
        Classify the query type based on patterns
        """
        for query_type, patterns in self.query_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query):
                    return query_type
        return 'general'
    
    def _handle_client_query(self, query, data):
        """Handle client-related queries"""
        client_data = data.get('client_analytics', {})
        
        if not client_data or not client_data.get('all_clients'):
            return "No client data available in your analyses."
        
        response = "📊 **Client Analysis:**\n\n"
        
        if 'top' in query or 'best' in query or 'highest' in query:
            top_clients = client_data.get('top_clients', [])[:5]
            response += "🏆 **Top Clients by Ticket Volume:**\n"
            for i, (client_name, client_info) in enumerate(top_clients, 1):
                tickets = client_info['total_tickets']
                files = client_info['files_appeared']
                avg_sentiment = client_info.get('avg_sentiment', 0)
                response += f"{i}. **{client_name}**: {tickets} tickets across {files} files (Sentiment: {avg_sentiment:.2f})\n"
        
        elif 'worst' in query or 'lowest' in query or 'concerning' in query:
            # Find clients with worst sentiment
            all_clients = client_data.get('all_clients', {})
            worst_clients = sorted(
                [(name, info) for name, info in all_clients.items() if info.get('avg_sentiment', 0) < 0],
                key=lambda x: x[1].get('avg_sentiment', 0)
            )[:5]
            
            if worst_clients:
                response += "⚠️ **Clients with Concerning Sentiment:**\n"
                for i, (client_name, client_info) in enumerate(worst_clients, 1):
                    sentiment = client_info.get('avg_sentiment', 0)
                    tickets = client_info['total_tickets']
                    response += f"{i}. **{client_name}**: {sentiment:.2f} sentiment score ({tickets} tickets)\n"
            else:
                response += "✅ No clients with concerning sentiment scores found.\n"
        
        else:
            # General client overview
            total_clients = client_data.get('client_count', 0)
            total_tickets = client_data.get('total_client_tickets', 0)
            avg_tickets = client_data.get('avg_tickets_per_client', 0)
            
            response += f"👥 **Client Overview:**\n"
            response += f"• Total Clients: {total_clients}\n"
            response += f"• Total Client Tickets: {total_tickets}\n"
            response += f"• Average Tickets per Client: {avg_tickets:.1f}\n\n"
            
            # Show top 3 clients
            top_clients = client_data.get('top_clients', [])[:3]
            if top_clients:
                response += "🔝 **Top 3 Clients:**\n"
                for i, (client_name, client_info) in enumerate(top_clients, 1):
                    tickets = client_info['total_tickets']
                    response += f"{i}. {client_name}: {tickets} tickets\n"
        
        return response
    
    def _handle_temporal_query(self, query, data):
        """Handle time-related queries"""
        temporal_data = data.get('temporal_analysis', {})
        
        if not temporal_data:
            return "No temporal data available for analysis."
        
        response = "📈 **Temporal Analysis:**\n\n"
        
        timeline = temporal_data.get('timeline', [])
        trends = temporal_data.get('trends', {})
        date_range = temporal_data.get('date_range', {})
        
        if date_range:
            span_days = date_range.get('span_days', 0)
            response += f"📅 **Analysis Period:** {date_range.get('start')} to {date_range.get('end')} ({span_days} days)\n\n"
        
        if trends:
            response += "📊 **Trends Identified:**\n"
            
            issue_trend = trends.get('issue_trend', 'stable')
            issue_change = trends.get('issue_change', 0)
            response += f"• Issue Volume: {issue_trend.title()} ({issue_change:+d} issues)\n"
            
            client_trend = trends.get('client_trend', 'stable')
            client_change = trends.get('client_change', 0)
            response += f"• Client Count: {client_trend.title()} ({client_change:+d} clients)\n"
            
            sentiment_trend = trends.get('sentiment_trend', 'stable')
            sentiment_change = trends.get('sentiment_change', 0)
            response += f"• Sentiment: {sentiment_trend.title()} ({sentiment_change:+.2f} score change)\n\n"
        
        if 'recent' in query or 'latest' in query:
            if timeline:
                latest = timeline[-1]
                response += f"🕐 **Latest Analysis ({latest['date']}):**\n"
                response += f"• File: {latest['file']}\n"
                response += f"• Issues: {latest['issue_count']}\n"
                response += f"• Clients: {latest['client_count']}\n"
                response += f"• Sentiment Score: {latest['sentiment_score']:.2f}\n"
        
        return response
    
    def _handle_priority_query(self, query, data):
        """Handle priority-related queries"""
        priority_data = data.get('priority_insights', {})
        
        if not priority_data:
            return "No priority data available in your analyses."
        
        response = "🚨 **Priority Analysis:**\n\n"
        
        priority_totals = priority_data.get('priority_totals', {})
        priority_percentages = priority_data.get('priority_percentages', {})
        total_issues = priority_data.get('total_issues', 0)
        
        response += f"📊 **Priority Distribution ({total_issues} total issues):**\n"
        
        # Sort priorities by count
        sorted_priorities = sorted(priority_totals.items(), key=lambda x: x[1], reverse=True)
        
        for priority, count in sorted_priorities:
            percentage = priority_percentages.get(priority, 0)
            emoji = self._get_priority_emoji(priority)
            response += f"{emoji} **{priority}**: {count} issues ({percentage}%)\n"
        
        # Highlight highest priority
        highest_priority = priority_data.get('highest_priority')
        if highest_priority:
            priority_name, priority_count = highest_priority
            response += f"\n⚠️ **Most Common Priority:** {priority_name} ({priority_count} issues)\n"
        
        return response
    
    def _handle_sentiment_query(self, query, data):
        """Handle sentiment-related queries"""
        sentiment_data = data.get('sentiment_overview', {})
        
        if not sentiment_data:
            return "No sentiment data available in your analyses."
        
        response = "😊 **Sentiment Analysis:**\n\n"
        
        sentiment_totals = sentiment_data.get('sentiment_totals', {})
        sentiment_percentages = sentiment_data.get('sentiment_percentages', {})
        total_tickets = sentiment_data.get('total_sentiment_tickets', 0)
        overall_score = sentiment_data.get('overall_sentiment_score', 0)
        
        response += f"📊 **Overall Sentiment Score:** {overall_score:.2f} (out of 1.0)\n"
        response += f"🎫 **Total Analyzed Tickets:** {total_tickets}\n\n"
        
        response += "📈 **Sentiment Distribution:**\n"
        
        # Sort sentiments for better display
        sentiment_order = ['positive', 'neutral', 'negative_low', 'negative_medium', 'negative_high']
        
        for sentiment_type in sentiment_order:
            if sentiment_type in sentiment_totals:
                count = sentiment_totals[sentiment_type]
                percentage = sentiment_percentages.get(sentiment_type, 0)
                emoji = self._get_sentiment_emoji(sentiment_type)
                display_name = sentiment_type.replace('_', ' ').title()
                response += f"{emoji} **{display_name}**: {count} tickets ({percentage}%)\n"
        
        # Sentiment interpretation
        if overall_score > 0.3:
            response += "\n✅ **Overall sentiment is positive!**\n"
        elif overall_score < -0.3:
            response += "\n⚠️ **Overall sentiment is concerning. Consider reviewing client feedback.**\n"
        else:
            response += "\n➖ **Overall sentiment is neutral.**\n"
        
        return response
    
    def _handle_file_query(self, query, data):
        """Handle file-related queries"""
        file_data = data.get('file_summary', {})
        
        if not file_data:
            return "No file data available."
        
        response = "📁 **File Analysis:**\n\n"
        
        files = file_data.get('files', [])
        summary = file_data.get('summary', {})
        
        response += f"📊 **Summary:**\n"
        response += f"• Total Files: {len(files)}\n"
        response += f"• Average Issues per File: {summary.get('avg_issues_per_file', 0):.1f}\n"
        response += f"• Total Unique Clients: {summary.get('total_unique_clients', 0)}\n"
        response += f"• Most Common Priority: {summary.get('most_common_priority', 'N/A')}\n\n"
        
        if 'latest' in query or 'newest' in query or 'recent' in query:
            if files:
                latest_file = files[0]  # Files are ordered by creation date desc
                response += f"🕐 **Latest File:**\n"
                response += f"• Name: {latest_file['filename']}\n"
                response += f"• Analysis Date: {latest_file['analysis_date']}\n"
                response += f"• Issues: {latest_file['issue_count']}\n"
                response += f"• Clients: {latest_file['client_count']}\n"
                response += f"• Sentiment: {latest_file['sentiment_summary']}\n"
        
        elif 'oldest' in query or 'first' in query:
            if files:
                oldest_file = files[-1]  # Last in the list
                response += f"📅 **Oldest File:**\n"
                response += f"• Name: {oldest_file['filename']}\n"
                response += f"• Analysis Date: {oldest_file['analysis_date']}\n"
                response += f"• Issues: {oldest_file['issue_count']}\n"
                response += f"• Clients: {oldest_file['client_count']}\n"
        
        else:
            # Show all files summary
            response += f"📋 **All Files ({len(files)}):**\n"
            for i, file_info in enumerate(files[:5], 1):  # Show top 5
                response += f"{i}. **{file_info['filename']}**: {file_info['issue_count']} issues, {file_info['client_count']} clients\n"
            
            if len(files) > 5:
                response += f"... and {len(files) - 5} more files\n"
        
        return response
    
    def _handle_statistical_query(self, query, data):
        """Handle statistical queries"""
        user_overview = data.get('user_overview', {})
        performance_metrics = data.get('performance_metrics', {})
        
        response = "📊 **Statistical Overview:**\n\n"
        
        # Basic statistics
        response += f"📈 **Key Metrics:**\n"
        response += f"• Total Files: {user_overview.get('total_files', 0)}\n"
        response += f"• Total Analyses: {user_overview.get('total_analyses', 0)}\n"
        response += f"• Total Issues: {user_overview.get('total_issues', 0)}\n"
        response += f"• Analysis Period: {user_overview.get('date_range_days', 0)} days\n\n"
        
        # Performance metrics
        if performance_metrics:
            response += f"⚡ **Performance Metrics:**\n"
            response += f"• Average Issues per File: {performance_metrics.get('avg_issues_per_file', 0)}\n"
            response += f"• Largest File: {performance_metrics.get('largest_file_size', 0)} issues\n"
            response += f"• Smallest File: {performance_metrics.get('smallest_file_size', 0)} issues\n"
            response += f"• Analysis Efficiency: {performance_metrics.get('analysis_efficiency', 0)}%\n"
            response += f"• Data Quality Score: {performance_metrics.get('data_quality_score', 0)}%\n"
        
        return response
    
    def _handle_performance_query(self, query, data):
        """Handle performance and recommendation queries"""
        response = "💡 **Performance Insights & Recommendations:**\n\n"
        
        # Analyze data for recommendations
        sentiment_data = data.get('sentiment_overview', {})
        priority_data = data.get('priority_insights', {})
        client_data = data.get('client_analytics', {})
        temporal_data = data.get('temporal_analysis', {})
        
        recommendations = []
        
        # Sentiment-based recommendations
        if sentiment_data:
            overall_score = sentiment_data.get('overall_sentiment_score', 0)
            if overall_score < -0.2:
                recommendations.append("🔴 **Urgent**: Overall sentiment is negative. Review client feedback and implement improvement measures.")
            
            negative_percentage = sentiment_data.get('sentiment_percentages', {}).get('negative_high', 0)
            if negative_percentage > 20:
                recommendations.append(f"⚠️ **High Priority**: {negative_percentage}% of tickets show high negative sentiment. Focus on these critical issues.")
        
        # Priority-based recommendations
        if priority_data:
            priority_totals = priority_data.get('priority_totals', {})
            critical_count = priority_totals.get('Critical', 0) + priority_totals.get('High', 0)
            total_issues = priority_data.get('total_issues', 0)
            
            if total_issues > 0 and critical_count / total_issues > 0.3:
                recommendations.append(f"🚨 **Process Improvement**: {critical_count} high/critical priority issues ({critical_count/total_issues*100:.1f}%). Consider workflow optimization.")
        
        # Client-based recommendations
        if client_data:
            all_clients = client_data.get('all_clients', {})
            problematic_clients = [name for name, info in all_clients.items() if info.get('avg_sentiment', 0) < -0.5]
            
            if problematic_clients:
                recommendations.append(f"👥 **Client Focus**: {len(problematic_clients)} clients need immediate attention: {', '.join(problematic_clients[:3])}")
        
        # Temporal recommendations
        if temporal_data:
            trends = temporal_data.get('trends', {})
            if trends.get('sentiment_trend') == 'declining':
                recommendations.append("📉 **Trend Alert**: Sentiment is declining over time. Investigate recent changes and implement corrective actions.")
            
            if trends.get('issue_trend') == 'increasing':
                recommendations.append("📈 **Capacity Planning**: Issue volume is increasing. Consider resource allocation and process improvements.")
        
        # Display recommendations
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                response += f"{i}. {rec}\n\n"
        else:
            response += "✅ **Good News**: Your data shows healthy patterns. Continue current practices and monitor for changes.\n\n"
        
        # General best practices
        response += "🎯 **General Best Practices:**\n"
        response += "• Monitor sentiment trends weekly\n"
        response += "• Address high-priority issues within 24 hours\n"
        response += "• Maintain regular client communication\n"
        response += "• Review and optimize workflows monthly\n"
        response += "• Track key metrics consistently\n"
        
        return response
    
    def _handle_general_query(self, query, data):
        """Handle general queries with comprehensive overview"""
        response = "📋 **Comprehensive Data Overview:**\n\n"
        
        # Quick summary from all data sources
        user_overview = data.get('user_overview', {})
        client_data = data.get('client_analytics', {})
        sentiment_data = data.get('sentiment_overview', {})
        priority_data = data.get('priority_insights', {})
        
        response += f"📊 **Quick Stats:**\n"
        response += f"• Files Analyzed: {user_overview.get('total_files', 0)}\n"
        response += f"• Total Issues: {user_overview.get('total_issues', 0)}\n"
        response += f"• Unique Clients: {client_data.get('client_count', 0)}\n"
        response += f"• Overall Sentiment: {sentiment_data.get('overall_sentiment_score', 0):.2f}\n"
        response += f"• Analysis Period: {user_overview.get('date_range_days', 0)} days\n\n"
        
        response += "💡 **What would you like to explore?**\n"
        response += "• Ask about specific clients: 'Show me top clients'\n"
        response += "• Check trends: 'How has sentiment changed over time?'\n"
        response += "• Review priorities: 'What are my critical issues?'\n"
        response += "• Get recommendations: 'How can I improve performance?'\n"
        response += "• File analysis: 'Show me my latest file analysis'\n"
        
        return response
    
    # Helper methods
    def _get_priority_emoji(self, priority):
        """Get emoji for priority level"""
        priority_lower = priority.lower()
        if 'critical' in priority_lower or 'highest' in priority_lower:
            return '🔴'
        elif 'high' in priority_lower:
            return '🟠'
        elif 'medium' in priority_lower:
            return '🟡'
        elif 'low' in priority_lower:
            return '🟢'
        else:
            return '⚪'
    
    def _get_sentiment_emoji(self, sentiment_type):
        """Get emoji for sentiment type"""
        if sentiment_type == 'positive':
            return '😊'
        elif sentiment_type == 'neutral':
            return '😐'
        elif sentiment_type == 'negative_low':
            return '😕'
        elif sentiment_type == 'negative_medium':
            return '😞'
        elif sentiment_type == 'negative_high':
            return '😡'
        else:
            return '❓'
