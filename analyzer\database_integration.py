"""
Database Integration Module for AI Agent
Provides comprehensive database access and query capabilities
"""
import logging
from django.db import connection
from django.db.models import <PERSON>, <PERSON>v<PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Q
from django.utils import timezone
from datetime import datetime, timedelta
from .models import AnalysisResult, JiraFile
from django.contrib.auth.models import User
from asgiref.sync import sync_to_async
import json

logger = logging.getLogger(__name__)

class DatabaseIntegrator:
    """
    Comprehensive database integration for AI Agent
    Provides real-time data access and intelligent query capabilities
    """
    
    def __init__(self, user):
        self.user = user
        self.cache = {}
        self.cache_timeout = 300  # 5 minutes cache
        
    async def get_comprehensive_user_data(self):
        """
        Get comprehensive data overview for the user
        """
        try:
            data = {
                'user_overview': await sync_to_async(self._get_user_overview)(),
                'file_summary': await sync_to_async(self._get_file_summary)(),
                'client_analytics': await sync_to_async(self._get_client_analytics)(),
                'temporal_analysis': await sync_to_async(self._get_temporal_analysis)(),
                'priority_insights': await sync_to_async(self._get_priority_insights)(),
                'sentiment_overview': await sync_to_async(self._get_sentiment_overview)(),
                'performance_metrics': await sync_to_async(self._get_performance_metrics)()
            }

            logger.info(f"Retrieved comprehensive data for user {self.user.username}")
            return data

        except Exception as e:
            logger.error(f"Error getting comprehensive user data: {str(e)}")
            return {}
    
    def _get_user_overview(self):
        """Get basic user statistics"""
        try:
            files = list(JiraFile.objects.filter(user=self.user))
            analyses = list(AnalysisResult.objects.filter(jira_file__user=self.user))
            
            return {
                'total_files': len(files),
                'total_analyses': len(analyses),
                'first_upload': min(f.uploaded_at for f in files) if files else None,
                'latest_upload': max(f.uploaded_at for f in files) if files else None,
                'total_issues': sum(analysis.issue_count for analysis in analyses),
                'date_range_days': self._calculate_date_range_from_list(files)
            }
        except Exception as e:
            logger.error(f"Error getting user overview: {str(e)}")
            return {}
    
    def _get_file_summary(self):
        """Get detailed file analysis summary"""
        try:
            analyses = list(AnalysisResult.objects.filter(
                jira_file__user=self.user
            ).select_related('jira_file').order_by('-created_at'))
            
            file_data = []
            for analysis in analyses:
                file_info = {
                    'id': analysis.id,
                    'filename': analysis.jira_file.original_filename or analysis.jira_file.file.name,
                    'analysis_date': analysis.jira_file.analysis_date,
                    'upload_date': analysis.jira_file.uploaded_at,
                    'issue_count': analysis.issue_count,
                    'client_count': len(analysis.client_metrics) if analysis.client_metrics else 0,
                    'priority_distribution': analysis.priority_distribution,
                    'status_distribution': analysis.status_distribution,
                    'ticket_types': analysis.ticket_types,
                    'sentiment_summary': self._summarize_sentiment(analysis.sentiment_analysis)
                }
                file_data.append(file_info)
            
            return {
                'files': file_data,
                'summary': {
                    'avg_issues_per_file': sum(f['issue_count'] for f in file_data) / len(file_data) if file_data else 0,
                    'total_unique_clients': len(self._get_all_unique_clients()),
                    'most_common_priority': self._get_most_common_priority(),
                    'most_common_status': self._get_most_common_status()
                }
            }
        except Exception as e:
            logger.error(f"Error getting file summary: {str(e)}")
            return {}
    
    def _get_client_analytics(self):
        """Get comprehensive client analytics"""
        try:
            all_clients = {}
            analyses = list(AnalysisResult.objects.filter(jira_file__user=self.user))
            
            for analysis in analyses:
                if not analysis.client_metrics:
                    continue
                    
                for client_name, metrics in analysis.client_metrics.items():
                    if client_name not in all_clients:
                        all_clients[client_name] = {
                            'total_tickets': 0,
                            'files_appeared': 0,
                            'experience_scores': [],
                            'sentiment_scores': [],
                            'first_seen': analysis.created_at,
                            'last_seen': analysis.created_at,
                            'ticket_history': []
                        }
                    
                    client_data = all_clients[client_name]
                    client_data['total_tickets'] += metrics.get('Tickets', 0)
                    client_data['files_appeared'] += 1
                    
                    if metrics.get('Customer_Experience_Score'):
                        client_data['experience_scores'].append(metrics.get('Customer_Experience_Score'))
                    
                    if metrics.get('sentiment'):
                        client_data['sentiment_scores'].append(metrics.get('sentiment'))
                    
                    client_data['ticket_history'].append({
                        'date': analysis.jira_file.analysis_date or analysis.created_at,
                        'tickets': metrics.get('Tickets', 0),
                        'file': analysis.jira_file.original_filename or analysis.jira_file.file.name
                    })
                    
                    if analysis.created_at < client_data['first_seen']:
                        client_data['first_seen'] = analysis.created_at
                    if analysis.created_at > client_data['last_seen']:
                        client_data['last_seen'] = analysis.created_at
            
            # Calculate averages and rankings
            for client_name, data in all_clients.items():
                if data['experience_scores']:
                    data['avg_experience_score'] = sum(data['experience_scores']) / len(data['experience_scores'])
                if data['sentiment_scores']:
                    data['avg_sentiment'] = sum(data['sentiment_scores']) / len(data['sentiment_scores'])
                
                # Sort ticket history by date
                data['ticket_history'].sort(key=lambda x: x['date'])
            
            # Rank clients by total tickets
            ranked_clients = sorted(all_clients.items(), key=lambda x: x[1]['total_tickets'], reverse=True)
            
            return {
                'all_clients': all_clients,
                'top_clients': ranked_clients[:10],
                'client_count': len(all_clients),
                'total_client_tickets': sum(data['total_tickets'] for data in all_clients.values()),
                'avg_tickets_per_client': sum(data['total_tickets'] for data in all_clients.values()) / len(all_clients) if all_clients else 0
            }
        except Exception as e:
            logger.error(f"Error getting client analytics: {str(e)}")
            return {}
    
    def _get_temporal_analysis(self):
        """Get temporal analysis and trends"""
        try:
            analyses = AnalysisResult.objects.filter(
                jira_file__user=self.user
            ).order_by('jira_file__analysis_date', 'created_at')
            
            if not analyses:
                return {}
            
            timeline = []
            for analysis in analyses:
                date = analysis.jira_file.analysis_date or analysis.created_at.date()
                timeline.append({
                    'date': date,
                    'issue_count': analysis.issue_count,
                    'client_count': len(analysis.client_metrics) if analysis.client_metrics else 0,
                    'file': analysis.jira_file.original_filename or analysis.jira_file.file.name,
                    'sentiment_score': self._calculate_sentiment_score(analysis.sentiment_analysis)
                })
            
            # Calculate trends
            trends = self._calculate_trends(timeline)
            
            return {
                'timeline': timeline,
                'trends': trends,
                'date_range': {
                    'start': timeline[0]['date'] if timeline else None,
                    'end': timeline[-1]['date'] if timeline else None,
                    'span_days': (timeline[-1]['date'] - timeline[0]['date']).days if len(timeline) > 1 else 0
                }
            }
        except Exception as e:
            logger.error(f"Error getting temporal analysis: {str(e)}")
            return {}
    
    def _get_priority_insights(self):
        """Get priority distribution insights"""
        try:
            analyses = AnalysisResult.objects.filter(jira_file__user=self.user)
            priority_totals = {}
            
            for analysis in analyses:
                if analysis.priority_distribution:
                    for priority, count in analysis.priority_distribution.items():
                        if priority not in priority_totals:
                            priority_totals[priority] = 0
                        priority_totals[priority] += count
            
            total_issues = sum(priority_totals.values())
            priority_percentages = {
                priority: round(count / total_issues * 100, 1) 
                for priority, count in priority_totals.items()
            } if total_issues > 0 else {}
            
            return {
                'priority_totals': priority_totals,
                'priority_percentages': priority_percentages,
                'highest_priority': max(priority_totals.items(), key=lambda x: x[1]) if priority_totals else None,
                'total_issues': total_issues
            }
        except Exception as e:
            logger.error(f"Error getting priority insights: {str(e)}")
            return {}
    
    def _get_sentiment_overview(self):
        """Get comprehensive sentiment analysis"""
        try:
            analyses = AnalysisResult.objects.filter(jira_file__user=self.user)
            sentiment_totals = {}
            sentiment_timeline = []
            
            for analysis in analyses:
                if analysis.sentiment_analysis:
                    # Aggregate totals
                    for sentiment_type, count in analysis.sentiment_analysis.items():
                        if sentiment_type not in sentiment_totals:
                            sentiment_totals[sentiment_type] = 0
                        sentiment_totals[sentiment_type] += count
                    
                    # Timeline entry
                    sentiment_timeline.append({
                        'date': analysis.jira_file.analysis_date or analysis.created_at.date(),
                        'file': analysis.jira_file.original_filename or analysis.jira_file.file.name,
                        'sentiment_distribution': analysis.sentiment_analysis,
                        'sentiment_score': self._calculate_sentiment_score(analysis.sentiment_analysis)
                    })
            
            total_sentiment_tickets = sum(sentiment_totals.values())
            sentiment_percentages = {
                sentiment: round(count / total_sentiment_tickets * 100, 1)
                for sentiment, count in sentiment_totals.items()
            } if total_sentiment_tickets > 0 else {}
            
            return {
                'sentiment_totals': sentiment_totals,
                'sentiment_percentages': sentiment_percentages,
                'sentiment_timeline': sentiment_timeline,
                'total_sentiment_tickets': total_sentiment_tickets,
                'overall_sentiment_score': self._calculate_overall_sentiment_score(sentiment_totals)
            }
        except Exception as e:
            logger.error(f"Error getting sentiment overview: {str(e)}")
            return {}
    
    def _get_performance_metrics(self):
        """Get performance and efficiency metrics"""
        try:
            analyses = AnalysisResult.objects.filter(jira_file__user=self.user)
            
            if not analyses:
                return {}
            
            # Calculate various performance metrics
            total_files = analyses.count()
            total_issues = sum(analysis.issue_count for analysis in analyses)
            avg_issues_per_file = total_issues / total_files if total_files > 0 else 0
            
            # File size analysis
            file_sizes = []
            for analysis in analyses:
                if analysis.issue_count > 0:
                    file_sizes.append(analysis.issue_count)
            
            metrics = {
                'total_files_analyzed': total_files,
                'total_issues_processed': total_issues,
                'avg_issues_per_file': round(avg_issues_per_file, 1),
                'largest_file_size': max(file_sizes) if file_sizes else 0,
                'smallest_file_size': min(file_sizes) if file_sizes else 0,
                'analysis_efficiency': self._calculate_analysis_efficiency(analyses),
                'data_quality_score': self._calculate_data_quality_score(analyses)
            }
            
            return metrics
        except Exception as e:
            logger.error(f"Error getting performance metrics: {str(e)}")
            return {}
    
    # Helper methods
    def _calculate_date_range_from_list(self, files):
        """Calculate date range in days from file list"""
        if len(files) < 2:
            return 0
        upload_dates = [f.uploaded_at for f in files]
        first = min(upload_dates)
        last = max(upload_dates)
        return (last - first).days
    
    def _summarize_sentiment(self, sentiment_data):
        """Summarize sentiment data"""
        if not sentiment_data:
            return "No sentiment data"
        
        total = sum(sentiment_data.values())
        if total == 0:
            return "No sentiment data"
        
        negative_count = sentiment_data.get('negative_low', 0) + sentiment_data.get('negative_medium', 0) + sentiment_data.get('negative_high', 0)
        negative_pct = round(negative_count / total * 100, 1)
        
        if negative_pct > 60:
            return f"Concerning ({negative_pct}% negative)"
        elif negative_pct > 40:
            return f"Mixed ({negative_pct}% negative)"
        else:
            return f"Positive ({negative_pct}% negative)"
    
    def _get_all_unique_clients(self):
        """Get all unique clients across all analyses"""
        clients = set()
        analyses = list(AnalysisResult.objects.filter(jira_file__user=self.user))
        for analysis in analyses:
            if analysis.client_metrics:
                clients.update(analysis.client_metrics.keys())
        return clients
    
    def _get_most_common_priority(self):
        """Get most common priority across all files"""
        priority_totals = {}
        analyses = list(AnalysisResult.objects.filter(jira_file__user=self.user))
        
        for analysis in analyses:
            if analysis.priority_distribution:
                for priority, count in analysis.priority_distribution.items():
                    if priority not in priority_totals:
                        priority_totals[priority] = 0
                    priority_totals[priority] += count
        
        return max(priority_totals.items(), key=lambda x: x[1])[0] if priority_totals else None
    
    def _get_most_common_status(self):
        """Get most common status across all files"""
        status_totals = {}
        analyses = list(AnalysisResult.objects.filter(jira_file__user=self.user))
        
        for analysis in analyses:
            if analysis.status_distribution:
                for status, count in analysis.status_distribution.items():
                    if status not in status_totals:
                        status_totals[status] = 0
                    status_totals[status] += count
        
        return max(status_totals.items(), key=lambda x: x[1])[0] if status_totals else None
    
    def _calculate_sentiment_score(self, sentiment_data):
        """Calculate overall sentiment score (-1 to 1)"""
        if not sentiment_data:
            return 0
        
        total = sum(sentiment_data.values())
        if total == 0:
            return 0
        
        # Weight different sentiment types
        score = 0
        score += sentiment_data.get('positive', 0) * 1
        score += sentiment_data.get('neutral', 0) * 0
        score += sentiment_data.get('negative_low', 0) * -0.3
        score += sentiment_data.get('negative_medium', 0) * -0.6
        score += sentiment_data.get('negative_high', 0) * -1
        
        return round(score / total, 2)
    
    def _calculate_overall_sentiment_score(self, sentiment_totals):
        """Calculate overall sentiment score from totals"""
        return self._calculate_sentiment_score(sentiment_totals)
    
    def _calculate_trends(self, timeline):
        """Calculate trends from timeline data"""
        if len(timeline) < 2:
            return {}
        
        # Issue count trend
        issue_counts = [entry['issue_count'] for entry in timeline]
        issue_trend = 'increasing' if issue_counts[-1] > issue_counts[0] else 'decreasing' if issue_counts[-1] < issue_counts[0] else 'stable'
        
        # Client count trend
        client_counts = [entry['client_count'] for entry in timeline]
        client_trend = 'increasing' if client_counts[-1] > client_counts[0] else 'decreasing' if client_counts[-1] < client_counts[0] else 'stable'
        
        # Sentiment trend
        sentiment_scores = [entry['sentiment_score'] for entry in timeline]
        sentiment_trend = 'improving' if sentiment_scores[-1] > sentiment_scores[0] else 'declining' if sentiment_scores[-1] < sentiment_scores[0] else 'stable'
        
        return {
            'issue_trend': issue_trend,
            'client_trend': client_trend,
            'sentiment_trend': sentiment_trend,
            'issue_change': issue_counts[-1] - issue_counts[0],
            'client_change': client_counts[-1] - client_counts[0],
            'sentiment_change': round(sentiment_scores[-1] - sentiment_scores[0], 2)
        }
    
    def _calculate_analysis_efficiency(self, analyses):
        """Calculate analysis efficiency score"""
        if not analyses:
            return 0
        
        # Simple efficiency metric based on data completeness
        complete_analyses = 0
        for analysis in analyses:
            score = 0
            if analysis.client_metrics:
                score += 1
            if analysis.sentiment_analysis:
                score += 1
            if analysis.priority_distribution:
                score += 1
            if analysis.status_distribution:
                score += 1
            if score >= 3:  # Consider complete if 3+ fields are populated
                complete_analyses += 1
        
        return round(complete_analyses / analyses.count() * 100, 1)
    
    def _calculate_data_quality_score(self, analyses):
        """Calculate data quality score"""
        if not analyses:
            return 0
        
        quality_scores = []
        for analysis in analyses:
            score = 0
            total_checks = 0
            
            # Check if issue count is reasonable
            if analysis.issue_count > 0:
                score += 1
            total_checks += 1
            
            # Check if client metrics exist and are populated
            if analysis.client_metrics and len(analysis.client_metrics) > 0:
                score += 1
            total_checks += 1
            
            # Check if sentiment analysis exists
            if analysis.sentiment_analysis and sum(analysis.sentiment_analysis.values()) > 0:
                score += 1
            total_checks += 1
            
            # Check if priority distribution exists
            if analysis.priority_distribution and len(analysis.priority_distribution) > 0:
                score += 1
            total_checks += 1
            
            quality_scores.append(score / total_checks * 100)
        
        return round(sum(quality_scores) / len(quality_scores), 1) if quality_scores else 0
