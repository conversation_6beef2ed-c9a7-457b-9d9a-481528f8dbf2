from django.db import models
from django.contrib.auth.models import User
from .models import JiraF<PERSON>, AnalysisResult

class ChatMessage(models.Model):
    MESSAGE_TYPES = [
        ('user', 'User Message'),
        ('ai', 'AI Response'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='chat_messages')
    conversation_id = models.UUIDField(help_text='Unique identifier for grouping messages in a conversation')
    message_type = models.CharField(max_length=10, choices=MESSAGE_TYPES)
    content = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    
    # References to related data
    related_file = models.ForeignKey(JiraFile, null=True, blank=True, on_delete=models.SET_NULL)
    related_analysis = models.ForeignKey(AnalysisResult, null=True, blank=True, on_delete=models.SET_NULL)
    
    # Metadata for tracking context and enhancing responses
    context_data = models.JSO<PERSON>ield(default=dict, help_text='Additional context like client names, date ranges, etc.')
    sentiment_score = models.FloatField(null=True, blank=True, help_text='Sentiment score of user message if applicable')
    intent_category = models.CharField(max_length=50, blank=True, help_text='Categorized intent of the message')
    
    class Meta:
        ordering = ['timestamp']
        indexes = [
            models.Index(fields=['conversation_id', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
        ]

    def __str__(self):
        return f"{self.get_message_type_display()} from {self.user.username} at {self.timestamp}"