{% extends 'base.html' %}

{% block title %}AI Agent - Vermeg Insights{% endblock %}

{% block extra_css %}
<style>
    .chat-container {
        height: 60vh;
        overflow-y: auto;
        padding: 1rem;
        background-color: var(--light);
        border-radius: var(--border-radius);
        margin-bottom: 1rem;
    }

    .message {
        margin-bottom: 1rem;
        max-width: 80%;
        display: flex;
        flex-direction: column;
    }

    .message.user-message {
        margin-left: auto;
        align-items: flex-end;
    }

    .message.ai-message {
        margin-right: auto;
        align-items: flex-start;
    }

    .message-content {
        padding: 0.75rem 1rem;
        border-radius: var(--border-radius);
        position: relative;
    }

    .user-message .message-content {
        background-color: var(--primary);
        color: var(--white);
    }

    .ai-message .message-content {
        background-color: var(--white);
        border: 1px solid var(--gray-medium);
        max-width: 100%;
    }

    .ai-message .message-content pre {
        background-color: var(--light);
        padding: 1rem;
        border-radius: var(--border-radius);
        overflow-x: auto;
        margin: 0.5rem 0;
        font-family: monospace;
        white-space: pre-wrap;
    }

    .ai-message .message-content code {
        background-color: var(--light);
        padding: 0.2rem 0.4rem;
        border-radius: 0.25rem;
        font-family: monospace;
    }

    .ai-message .message-content table {
        width: 100%;
        border-collapse: collapse;
        margin: 1rem 0;
    }

    .ai-message .message-content th,
    .ai-message .message-content td {
        border: 1px solid var(--gray-medium);
        padding: 0.5rem;
        text-align: left;
    }

    .ai-message .message-content th {
        background-color: var(--light);
        font-weight: bold;
    }

    .ai-message .message-content .chart-container {
        margin: 1rem 0;
        padding: 1rem;
        background-color: var(--light);
        border-radius: var(--border-radius);
        overflow-x: auto;
    }

    .ai-message .message-content .metric-card {
        background-color: var(--white);
        border: 1px solid var(--gray-medium);
        border-radius: var(--border-radius);
        padding: 1rem;
        margin: 0.5rem 0;
    }

    .ai-message .message-content .metric-card h4 {
        margin: 0 0 0.5rem 0;
        color: var(--primary);
    }

    .ai-message .message-content .metric-card .value {
        font-size: 1.5rem;
        font-weight: bold;
        color: var(--text-dark);
    }

    .ai-message .message-content .metric-card .trend {
        font-size: 0.875rem;
        color: var(--text-light);
    }

    .ai-message .message-content .trend-up {
        color: var(--success);
    }

    .ai-message .message-content .trend-down {
        color: var(--danger);
    }

    .message-timestamp {
        font-size: 0.8rem;
        color: var(--text-light);
        margin-top: 0.25rem;
    }

    .chat-input-container {
        background-color: var(--white);
        border-top: 1px solid var(--gray-medium);
        padding: 1rem;
        position: relative;
    }

    .loading-indicator {
        display: none;
        text-align: center;
        padding: 1rem;
        color: var(--text-light);
    }

    .loading-indicator.active {
        display: block;
    }

    .suggestions {
        margin-top: 1rem;
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .suggestion-chip {
        background-color: var(--light);
        border: 1px solid var(--gray-medium);
        border-radius: 1rem;
        padding: 0.25rem 0.75rem;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s;
    }

    .suggestion-chip:hover {
        background-color: var(--gray-medium);
    }

    .suggestions .category {
        width: 100%;
        margin-top: 0.5rem;
        font-size: 0.875rem;
        color: var(--text-light);
    }

    .suggestions .category:first-child {
        margin-top: 0;
    }

    .error-message {
        color: var(--danger);
        background-color: var(--danger-light);
        padding: 0.75rem 1rem;
        border-radius: var(--border-radius);
        margin-bottom: 1rem;
        display: none;
    }

    .error-message.active {
        display: block;
    }
</style>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/styles/default.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/marked/lib/marked.min.css">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <!-- Main Chat Interface -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">AI Agent</h5>
                </div>
                <div class="card-body">
                    <!-- Error Message -->
                    <div id="error-message" class="error-message"></div>

                    <!-- Loading Indicator -->
                    <div id="loading-indicator" class="loading-indicator">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div id="chat-messages" class="chat-messages">
                        <!-- Messages will be dynamically added here -->
                    </div>

                    <!-- Chat Input -->
                    <div class="chat-input-container">
                        <form id="chat-form" class="d-flex">
                            {% csrf_token %}
                            <input type="text" id="message-input" class="form-control" placeholder="Type your message...">
                            <button type="submit" class="btn btn-primary ms-2">Send</button>
                        </form>
                    </div>

                    <!-- Suggestion Chips -->
                    <div class="suggestions mt-3">
                        <div class="suggestion-category">
                            <h6>Data Analysis</h6>
                            <div class="suggestion-chips">
                                <button class="chip" data-query="Show me the latest ticket trends">Latest Trends</button>
                                <button class="chip" data-query="Analyze client satisfaction">Client Satisfaction</button>
                                <button class="chip" data-query="Compare performance metrics">Performance Metrics</button>
                            </div>
                        </div>
                        <div class="suggestion-category">
                            <h6>Critical Situations</h6>
                            <div class="suggestion-chips">
                                <button class="chip" data-query="Identify critical clients">Critical Clients</button>
                                <button class="chip" data-query="Show risk assessment">Risk Assessment</button>
                                <button class="chip" data-query="Generate action plans">Action Plans</button>
                            </div>
                        </div>
                        <div class="suggestion-category">
                            <h6>Proactive Management</h6>
                            <div class="suggestion-chips">
                                <button class="chip" data-query="Generate client communication">Client Communication</button>
                                <button class="chip" data-query="Suggest preventive measures">Preventive Measures</button>
                                <button class="chip" data-query="Show improvement opportunities">Improvement Opportunities</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Client Insights Panel -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Client Insights</h5>
                </div>
                <div class="card-body">
                    <!-- Critical Clients Section -->
                    <div id="critical-clients" class="insight-section">
                        <h6>Critical Clients</h6>
                        <div class="critical-clients-list">
                            <!-- Critical clients will be dynamically added here -->
                        </div>
                    </div>

                    <!-- Risk Indicators Section -->
                    <div id="risk-indicators" class="insight-section">
                        <h6>Risk Indicators</h6>
                        <div class="risk-indicators-list">
                            <!-- Risk indicators will be dynamically added here -->
                        </div>
                    </div>

                    <!-- Trend Analysis Section -->
                    <div id="trend-analysis" class="insight-section">
                        <h6>Trend Analysis</h6>
                        <div class="trend-analysis-content">
                            <!-- Trend analysis will be dynamically added here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const chatForm = document.getElementById('chat-form');
    const messageInput = document.getElementById('message-input');
    const chatMessages = document.getElementById('chat-messages');
    const loadingIndicator = document.getElementById('loading-indicator');
    const errorMessage = document.getElementById('error-message');
    let conversationId = null;

    // Function to add a message to the chat
    function addMessage(content, isUser = false) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${isUser ? 'user-message' : 'ai-message'}`;
        
        const messageContent = document.createElement('div');
        messageContent.className = 'message-content';
        
        if (isUser) {
            messageContent.textContent = content;
        } else {
            // Use marked.js to render markdown
            messageContent.innerHTML = marked.parse(content);
            
            // Initialize syntax highlighting
            messageContent.querySelectorAll('pre code').forEach((block) => {
                hljs.highlightElement(block);
            });
        }
        
        const timestamp = document.createElement('div');
        timestamp.className = 'message-timestamp';
        timestamp.textContent = new Date().toLocaleTimeString();
        
        messageDiv.appendChild(messageContent);
        messageDiv.appendChild(timestamp);
        chatMessages.appendChild(messageDiv);
        
        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // Function to show/hide loading indicator
    function setLoading(isLoading) {
        loadingIndicator.classList.toggle('active', isLoading);
        messageInput.disabled = isLoading;
        chatForm.querySelector('button').disabled = isLoading;
    }

    // Function to show error message
    function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.add('active');
        setTimeout(() => {
            errorMessage.classList.remove('active');
        }, 5000);
    }

    // Function to update insights panel
    function updateInsights(insights) {
        // Update critical clients
        const criticalClients = document.getElementById('critical-clients');
        if (criticalClients && insights.critical_clients) {
            criticalClients.innerHTML = insights.critical_clients.map(client => 
                `<div class="client-item">${client.client_name}</div>`
            ).join('');
        }

        // Update risk indicators
        const riskIndicators = document.getElementById('risk-indicators');
        if (riskIndicators && insights.risk_indicators) {
            riskIndicators.innerHTML = Object.entries(insights.risk_indicators)
                .map(([key, value]) => `<div class="risk-item">${key}: ${value}</div>`)
                .join('');
        }

        // Update trends
        const trends = document.getElementById('trends');
        if (trends && insights.trends) {
            trends.innerHTML = Object.entries(insights.trends)
                .map(([key, value]) => `<div class="trend-item">${key}: ${value}</div>`)
                .join('');
        }
    }

    // Handle form submission
    chatForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const message = messageInput.value.trim();
        if (!message) return;

        // Add user message to chat
        addMessage(message, true);
        messageInput.value = '';
        
        setLoading(true);
        errorMessage.classList.remove('active');

        try {
            const response = await fetch('/analyzer/api/chat/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    message: message,
                    conversation_id: conversationId
                })
            });

            if (!response.ok) {
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('text/html')) {
                    throw new Error('Server returned an HTML error page. Please check the server logs.');
                }
                const data = await response.json();
                throw new Error(data.error || 'Failed to send message');
            }

            // Update conversation ID if needed
            if (data.conversation_id) {
                conversationId = data.conversation_id;
            }

            // Add AI response to chat
            addMessage(data.response);

            // Update insights panel
            if (data.insights) {
                updateInsights(data.insights);
            }

        } catch (error) {
            console.error('Error:', error);
            showError(error.message);
        } finally {
            setLoading(false);
        }
    });

    // Handle suggestion chips
    document.querySelectorAll('.suggestion-chip').forEach(chip => {
        chip.addEventListener('click', function() {
            const query = this.dataset.query;
            messageInput.value = query;
            chatForm.dispatchEvent(new Event('submit'));
        });
    });
});
</script>
<script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.7.0/highlight.min.js"></script>
{% endblock %}