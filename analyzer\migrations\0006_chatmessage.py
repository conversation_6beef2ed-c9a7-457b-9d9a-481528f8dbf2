# Generated by Django 4.2.7 on 2025-05-31 01:51

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('analyzer', '0005_jirafile_analysis_date'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChatMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('conversation_id', models.UUIDField(help_text='Unique identifier for grouping messages in a conversation')),
                ('message_type', models.CharField(choices=[('user', 'User Message'), ('ai', 'AI Response')], max_length=10)),
                ('content', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('context_data', models.JSO<PERSON>ield(default=dict, help_text='Additional context like client names, date ranges, etc.')),
                ('sentiment_score', models.FloatField(blank=True, help_text='Sentiment score of user message if applicable', null=True)),
                ('intent_category', models.CharField(blank=True, help_text='Categorized intent of the message', max_length=50)),
                ('related_analysis', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='analyzer.analysisresult')),
                ('related_file', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='analyzer.jirafile')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='chat_messages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['timestamp'],
                'indexes': [models.Index(fields=['conversation_id', 'timestamp'], name='analyzer_ch_convers_a2616a_idx'), models.Index(fields=['user', 'timestamp'], name='analyzer_ch_user_id_6d864f_idx')],
            },
        ),
    ]
