import os
import requests
import logging
import json
from datetime import datetime, timedelta
from django.conf import settings
from dotenv import load_dotenv
from .chat_models import ChatMessage

# Set up logging
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

class OpenSourceLLMHandler:
    def __init__(self):
        """Initialize the Open Source LLM Handler with multiple fallback options"""
        
        # Try to get Hugging Face token (optional, but increases rate limits)
        self.hf_token = os.getenv('HUGGINGFACE_TOKEN', None)
        
        # Define available free models (in order of preference)
        self.available_models = [
            {
                "name": "microsoft/DialoGPT-large",
                "api_url": "https://api-inference.huggingface.co/models/microsoft/DialoGPT-large",
                "type": "conversational"
            },
            {
                "name": "microsoft/DialoGPT-medium", 
                "api_url": "https://api-inference.huggingface.co/models/microsoft/DialoGPT-medium",
                "type": "conversational"
            },
            {
                "name": "facebook/blenderbot-400M-distill",
                "api_url": "https://api-inference.huggingface.co/models/facebook/blenderbot-400M-distill",
                "type": "conversational"
            },
            {
                "name": "google/flan-t5-large",
                "api_url": "https://api-inference.huggingface.co/models/google/flan-t5-large", 
                "type": "text-generation"
            }
        ]
        
        # Set current model (will be determined dynamically)
        self.current_model = None
        
        # Define system prompt for Jira ticket analysis context
        self.system_prompt = """You are an AI assistant specialized in analyzing Jira ticket data and providing insights. 
        You help users understand:
        - Ticket metrics and distributions
        - Client-specific data and trends  
        - Sentiment analysis results
        - Historical patterns and trends
        - Best practices for project management
        
        Provide clear, actionable insights backed by data when possible.
        Keep responses concise but helpful."""

    def _make_api_request(self, model_info, prompt, max_retries=2):
        """Make API request to Hugging Face Inference API"""
        headers = {
            "Content-Type": "application/json"
        }
        
        # Add authorization header if token is available
        if self.hf_token:
            headers["Authorization"] = f"Bearer {self.hf_token}"
        
        # Prepare payload based on model type
        if model_info["type"] == "conversational":
            payload = {
                "inputs": {
                    "past_user_inputs": [],
                    "generated_responses": [],
                    "text": prompt
                },
                "parameters": {
                    "max_length": 500,
                    "temperature": 0.7,
                    "do_sample": True
                }
            }
        else:  # text-generation
            payload = {
                "inputs": prompt,
                "parameters": {
                    "max_new_tokens": 300,
                    "temperature": 0.7,
                    "do_sample": True,
                    "return_full_text": False
                }
            }
        
        for attempt in range(max_retries + 1):
            try:
                response = requests.post(
                    model_info["api_url"],
                    headers=headers,
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # Extract response based on model type
                    if model_info["type"] == "conversational":
                        if isinstance(result, dict) and "generated_text" in result:
                            return result["generated_text"]
                        elif isinstance(result, list) and len(result) > 0:
                            return result[0].get("generated_text", "")
                    else:  # text-generation
                        if isinstance(result, list) and len(result) > 0:
                            return result[0].get("generated_text", "")
                        elif isinstance(result, dict) and "generated_text" in result:
                            return result["generated_text"]
                    
                    return str(result)  # Fallback
                    
                elif response.status_code == 503:
                    # Model is loading, wait and retry
                    if attempt < max_retries:
                        logger.info(f"Model {model_info['name']} is loading, retrying in 5 seconds...")
                        import time
                        time.sleep(5)
                        continue
                    else:
                        return None
                        
                else:
                    logger.error(f"API request failed with status {response.status_code}: {response.text}")
                    return None
                    
            except requests.exceptions.RequestException as e:
                logger.error(f"Request exception for model {model_info['name']}: {str(e)}")
                if attempt < max_retries:
                    continue
                return None
        
        return None

    async def generate_response(self, user_message, conversation_id=None, context_data=None):
        """Generate a response using free open-source LLMs"""
        try:
            # Prepare the prompt with context
            prompt = self._prepare_prompt(user_message, context_data)
            
            # Try each model until we get a successful response
            for model_info in self.available_models:
                logger.info(f"Trying model: {model_info['name']}")
                
                response = self._make_api_request(model_info, prompt)
                
                if response and response.strip():
                    self.current_model = model_info
                    logger.info(f"Successfully got response from {model_info['name']}")
                    
                    # Clean up the response
                    cleaned_response = self._clean_response(response, user_message)
                    return cleaned_response
                    
                logger.warning(f"Model {model_info['name']} failed or returned empty response")
            
            # If all models fail, return a helpful fallback response
            return self._get_fallback_response(user_message, context_data)
            
        except Exception as e:
            logger.error(f"Unexpected error in LLM integration: {str(e)}")
            return self._get_fallback_response(user_message, context_data)

    def _prepare_prompt(self, user_message, context_data=None):
        """Prepare the prompt with system context and user message"""
        prompt = f"{self.system_prompt}\n\n"
        
        # Add context data if available
        if context_data:
            prompt += self._format_context_data(context_data) + "\n\n"
        
        prompt += f"User: {user_message}\nAssistant:"
        return prompt

    def _format_context_data(self, context_data):
        """Format comprehensive context data with full file memory for AI analysis"""
        if not context_data or not context_data.get('latest_analysis'):
            return "No analysis data available. Please upload and analyze Jira files first."

        analysis = context_data['latest_analysis']
        context_message = "=== COMPREHENSIVE JIRA ANALYSIS CONTEXT ===\n\n"

        # Historical overview if multiple files exist
        if context_data.get('historical_summary') and context_data['historical_summary'].get('total_files_analyzed', 0) > 1:
            hist = context_data['historical_summary']
            context_message += f"📊 **HISTORICAL OVERVIEW** ({hist['total_files_analyzed']} files analyzed):\n"
            context_message += f"  • Total Issues Across All Files: {hist['total_issues_across_all_files']}\n"
            context_message += f"  • Average Issues Per File: {hist['average_issues_per_file']}\n"
            context_message += f"  • Analysis Period: {hist['date_range']['earliest']} to {hist['date_range']['latest']}\n"
            context_message += f"  • Time Span: {hist['date_range']['span_days']} days\n\n"

        # Current file analysis
        context_message += f"📁 **LATEST ANALYSIS**: {analysis.get('file_name', 'Unknown')}\n"
        context_message += f"📅 **Analysis Date**: {analysis.get('analysis_date', 'N/A')}\n"
        context_message += f"🎫 **Issues in This File**: {analysis.get('issue_count', 0)}\n\n"

        # Ticket types breakdown
        if analysis.get('ticket_types'):
            context_message += "📊 **Ticket Types**:\n"
            for ticket_type, count in analysis['ticket_types'].items():
                percentage = round(count / analysis.get('issue_count', 1) * 100, 1)
                context_message += f"  • {ticket_type}: {count} ({percentage}%)\n"
            context_message += "\n"

        # Priority distribution
        if analysis.get('priority_distribution'):
            context_message += "🚨 **Priority Distribution**:\n"
            total_issues = analysis.get('issue_count', 1)
            for priority, count in analysis['priority_distribution'].items():
                percentage = round(count / total_issues * 100, 1)
                context_message += f"  • {priority}: {count} ({percentage}%)\n"
            context_message += "\n"

        # Sentiment analysis
        if context_data.get('sentiment'):
            sentiment = context_data['sentiment']
            context_message += f"😊 **Sentiment Analysis** ({sentiment.get('total_tickets', 0)} tickets analyzed):\n"
            if sentiment.get('distribution'):
                for sentiment_type, count in sentiment['distribution'].items():
                    context_message += f"  • {sentiment_type.replace('_', ' ').title()}: {count}\n"
            context_message += f"  • **Negative Sentiment**: {sentiment.get('negative_percentage', 0)}%\n\n"

        # Comprehensive client analysis across all files
        if context_data.get('aggregated_client_metrics'):
            agg_clients = context_data['aggregated_client_metrics']
            context_message += f"👥 **COMPREHENSIVE CLIENT ANALYSIS** ({len(agg_clients)} total clients):\n"

            # Sort by total tickets across all files
            sorted_agg_clients = sorted(
                agg_clients.items(),
                key=lambda x: x[1].get('total_tickets', 0),
                reverse=True
            )

            for client, metrics in sorted_agg_clients[:5]:
                total_tickets = metrics.get('total_tickets', 0)
                files_count = metrics.get('files_appeared_in', 0)
                avg_experience = metrics.get('avg_experience_score', 0)
                avg_sentiment = metrics.get('avg_sentiment', 0)
                context_message += f"  • {client}: {total_tickets} total tickets across {files_count} files, "
                context_message += f"Avg Experience: {avg_experience}%, Avg Sentiment: {avg_sentiment:.2f}\n"
            context_message += "\n"

        # Current file clients (if different from aggregated)
        elif context_data.get('client_metrics'):
            context_message += f"👥 **Current File Clients** ({len(context_data['client_metrics'])} total):\n"
            sorted_clients = sorted(
                context_data['client_metrics'].items(),
                key=lambda x: x[1].get('Tickets', 0),
                reverse=True
            )
            for client, metrics in sorted_clients[:5]:
                tickets = metrics.get('Tickets', 0)
                sentiment_score = metrics.get('sentiment', 0)
                experience_score = round(metrics.get('Customer_Experience_Score', 0) * 100, 1)
                context_message += f"  • {client}: {tickets} tickets, Experience: {experience_score}%, Sentiment: {sentiment_score:.2f}\n"
            context_message += "\n"

        # Multi-file trends and comprehensive analysis
        if context_data.get('multi_file_trends'):
            trends = context_data['multi_file_trends']
            context_message += "📈 **MULTI-FILE TRENDS & PATTERNS**:\n"

            if trends.get('overall_direction'):
                direction = trends['overall_direction']
                context_message += f"  • Overall Trend: Issues are {direction}\n"

            if trends.get('issue_volume_trend'):
                volume_trend = trends['issue_volume_trend']
                if len(volume_trend) >= 2:
                    latest = volume_trend[-1]
                    context_message += f"  • Latest Change: {latest['change_from_previous']} issues ({latest['change_percentage']}%)\n"

            context_message += "\n"

        # Cross-file patterns
        if context_data.get('cross_file_patterns'):
            patterns = context_data['cross_file_patterns']
            context_message += "🔍 **CROSS-FILE PATTERNS**:\n"

            if patterns.get('recurring_clients'):
                recurring = patterns['recurring_clients']
                context_message += f"  • Recurring Clients: {len(recurring)} clients appear in multiple files\n"
                top_recurring = sorted(recurring.items(), key=lambda x: x[1], reverse=True)[:3]
                for client, count in top_recurring:
                    context_message += f"    - {client}: appears in {count} files\n"

            if patterns.get('consistent_priorities'):
                priorities = patterns['consistent_priorities']
                most_common = max(priorities.items(), key=lambda x: x[1]) if priorities else None
                if most_common:
                    context_message += f"  • Most Consistent Priority: {most_common[0]} (appears in {most_common[1]} files)\n"

            context_message += "\n"

        # Traditional trends and comparisons (for backward compatibility)
        elif context_data.get('comparative_data'):
            comp = context_data['comparative_data']
            context_message += "📈 **Recent Changes**:\n"

            issue_change = comp.get('issue_count_change', 0)
            issue_change_pct = comp.get('issue_count_change_percentage', 0)
            if issue_change != 0:
                direction = "increased" if issue_change > 0 else "decreased"
                context_message += f"  • Issue count {direction} by {abs(issue_change)} ({abs(issue_change_pct)}%)\n"

            if comp.get('new_clients'):
                context_message += f"  • New clients: {', '.join(comp['new_clients'])}\n"

            if comp.get('lost_clients'):
                context_message += f"  • Lost clients: {', '.join(comp['lost_clients'])}\n"

            context_message += "\n"

        # Data summary for quick reference
        if context_data.get('data_summary'):
            summary = context_data['data_summary']
            context_message += "📋 **Key Insights**:\n"
            context_message += f"  • Most common priority: {summary.get('top_priority', 'N/A')}\n"
            context_message += f"  • Most common ticket type: {summary.get('top_ticket_type', 'N/A')}\n"
            context_message += f"  • Sentiment status: {summary.get('sentiment_summary', 'N/A')}\n"
            context_message += f"  • Total files analyzed: {summary.get('total_files_analyzed', 0)}\n\n"

        # Actionable insights
        if context_data.get('actionable_insights'):
            context_message += "💡 **Existing Actionable Insights**:\n"
            for insight in context_data['actionable_insights'][:3]:  # Show top 3
                context_message += f"  • {insight}\n"
            context_message += "\n"

        context_message += "=== END CONTEXT ===\n"
        return context_message

    def _clean_response(self, response, user_message):
        """Clean and format the LLM response"""
        # Remove any repetition of the user message
        if user_message.lower() in response.lower():
            response = response.replace(user_message, "").strip()
        
        # Remove common prefixes
        prefixes_to_remove = ["Assistant:", "AI:", "Response:", "Answer:"]
        for prefix in prefixes_to_remove:
            if response.startswith(prefix):
                response = response[len(prefix):].strip()
        
        # Ensure response is not empty
        if not response.strip():
            return "I understand you're asking about Jira analysis. Could you please provide more specific details about what you'd like to know?"
        
        # Limit response length
        if len(response) > 1000:
            response = response[:1000] + "..."
        
        return response.strip()

    def _get_fallback_response(self, user_message, context_data=None):
        """Provide intelligent, data-driven fallback responses when LLMs are unavailable"""

        # Check if we have analysis data available
        has_data = context_data and context_data.get('latest_analysis')
        message_lower = user_message.lower()

        # Greeting responses
        if any(word in message_lower for word in ['hello', 'hi', 'hey', 'greetings']):
            if has_data:
                summary = context_data.get('data_summary', {})
                return f"Hello! I'm your Jira analysis assistant. I can see you have {summary.get('total_files_analyzed', 0)} files analyzed with {summary.get('total_issues_latest', 0)} total issues in your latest dataset. How can I help you understand your data better?"
            else:
                return "Hello! I'm your Jira analysis assistant powered by open-source AI. Upload some Jira files to get started with intelligent insights about your tickets, clients, and trends."

        # Data analysis requests
        elif any(word in message_lower for word in ['analysis', 'analyze', 'data', 'insights']):
            return self._generate_analysis_response(context_data)

        # Client-specific requests
        elif any(word in message_lower for word in ['client', 'customer']):
            return self._generate_client_response(context_data, message_lower)

        # Trend analysis requests
        elif any(word in message_lower for word in ['trend', 'pattern', 'historical', 'over time', 'compare']):
            return self._generate_trend_response(context_data)

        # Priority analysis requests
        elif any(word in message_lower for word in ['priority', 'urgent', 'critical', 'blocker']):
            return self._generate_priority_response(context_data)

        # Sentiment analysis requests
        elif any(word in message_lower for word in ['sentiment', 'satisfaction', 'experience', 'feedback']):
            return self._generate_sentiment_response(context_data)

        # Recommendation requests
        elif any(word in message_lower for word in ['recommend', 'suggest', 'improve', 'optimize']):
            return self._generate_recommendation_response(context_data)

        # Summary requests
        elif any(word in message_lower for word in ['summary', 'overview', 'report']):
            return self._generate_summary_response(context_data)

        # Help requests
        elif any(word in message_lower for word in ['help', 'what', 'how', 'can you']):
            return self._generate_help_response(has_data)

        # Default intelligent response
        else:
            return self._generate_default_response(context_data, user_message)

    def _generate_analysis_response(self, context_data):
        """Generate intelligent analysis response based on available data"""
        if not context_data or not context_data.get('latest_analysis'):
            return "📈 I can help you analyze your Jira data! Please upload some Jira files first, then I can provide insights about:\n• Ticket distributions and patterns\n• Priority and status analysis\n• Client-specific metrics\n• Trend identification\n\nWhat type of analysis interests you most?"

        analysis = context_data['latest_analysis']
        summary = context_data.get('data_summary', {})

        response = f"📊 **Analysis of {analysis.get('file_name', 'your data')}** (Date: {analysis.get('analysis_date', 'N/A')}):\n\n"
        response += f"🎫 **{analysis.get('issue_count', 0)} Total Issues**\n"

        # Ticket types breakdown
        if analysis.get('ticket_types'):
            response += "\n📋 **Ticket Types**:\n"
            for ticket_type, count in list(analysis['ticket_types'].items())[:3]:
                percentage = round(count / analysis.get('issue_count', 1) * 100, 1)
                response += f"• {ticket_type}: {count} ({percentage}%)\n"

        # Priority insights
        if analysis.get('priority_distribution'):
            top_priority = summary.get('top_priority', 'N/A')
            response += f"\n🚨 **Top Priority**: {top_priority}\n"

        # Client insights
        if context_data.get('client_metrics'):
            client_count = len(context_data['client_metrics'])
            response += f"\n👥 **{client_count} Clients** analyzed\n"

        # Sentiment insights
        if context_data.get('sentiment'):
            sentiment_summary = summary.get('sentiment_summary', 'N/A')
            response += f"\n😊 **Sentiment**: {sentiment_summary}\n"

        response += "\n💡 **What would you like to explore?**\n• Detailed client analysis\n• Priority trends\n• Sentiment breakdown\n• Comparative analysis\n• Actionable recommendations"

        return response

    def _generate_client_response(self, context_data, message_lower):
        """Generate client-specific analysis response"""
        if not context_data or not context_data.get('client_metrics'):
            return "👥 I can help you analyze client-specific metrics including:\n• Ticket volumes and types\n• Resolution times and efficiency\n• Satisfaction trends\n• Priority patterns\n\nPlease upload Jira data first to get client insights."

        client_metrics = context_data['client_metrics']
        response = f"👥 **Client Analysis** ({len(client_metrics)} clients):\n\n"

        # Check if specific client mentioned
        mentioned_client = None
        for client_name in client_metrics.keys():
            if client_name.lower() in message_lower:
                mentioned_client = client_name
                break

        if mentioned_client:
            # Specific client analysis
            metrics = client_metrics[mentioned_client]
            tickets = metrics.get('Tickets', 0)
            experience = round(metrics.get('Customer_Experience_Score', 0) * 100, 1)
            sentiment = metrics.get('sentiment', 0)

            response += f"🔍 **{mentioned_client}** Analysis:\n"
            response += f"• Tickets: {tickets}\n"
            response += f"• Experience Score: {experience}%\n"
            response += f"• Sentiment: {sentiment:.2f}\n"
            response += f"• Resolution Time: {metrics.get('Avg_Resolution_Time_Days', 'N/A')} days\n\n"
            response += "💡 Would you like recommendations for improving this client's experience?"
        else:
            # Top clients overview
            sorted_clients = sorted(client_metrics.items(), key=lambda x: x[1].get('Tickets', 0), reverse=True)
            response += "🏆 **Top Clients by Ticket Volume**:\n"
            for client, metrics in sorted_clients[:5]:
                tickets = metrics.get('Tickets', 0)
                experience = round(metrics.get('Customer_Experience_Score', 0) * 100, 1)
                response += f"• {client}: {tickets} tickets, {experience}% experience\n"

            response += "\n💡 **Ask about specific clients** or request:\n• Client comparison analysis\n• Experience improvement recommendations\n• Ticket volume trends"

        return response

    def _generate_trend_response(self, context_data):
        """Generate trend analysis response"""
        if not context_data or not context_data.get('trends'):
            return "📈 I can identify trends in your Jira data including:\n• Ticket volume changes over time\n• Priority distribution shifts\n• Resolution time patterns\n• Client satisfaction trends\n\nPlease upload multiple Jira files to enable trend analysis."

        trends = context_data['trends']
        comparative = context_data.get('comparative_data', {})

        response = "📈 **Trend Analysis**:\n\n"

        # Issue count trends
        if trends.get('issue_count_trend'):
            trend_data = trends['issue_count_trend']
            if len(trend_data) > 1:
                latest_count = trend_data[-1]['count']
                previous_count = trend_data[-2]['count']
                change = latest_count - previous_count
                direction = "increased" if change > 0 else "decreased"
                response += f"🎫 **Issue Volume**: {direction} by {abs(change)} tickets\n"

        # Priority trends
        if comparative.get('priority_changes'):
            response += "\n🚨 **Priority Changes**:\n"
            for priority, change_data in list(comparative['priority_changes'].items())[:3]:
                change = change_data['change']
                if change != 0:
                    direction = "↗️" if change > 0 else "↘️"
                    response += f"• {priority}: {direction} {abs(change)} tickets\n"

        # Client changes
        if comparative.get('new_clients') or comparative.get('lost_clients'):
            response += "\n👥 **Client Changes**:\n"
            if comparative.get('new_clients'):
                response += f"• New clients: {', '.join(comparative['new_clients'])}\n"
            if comparative.get('lost_clients'):
                response += f"• Lost clients: {', '.join(comparative['lost_clients'])}\n"

        response += "\n💡 **Insights**: Ask for specific trend analysis or recommendations based on these patterns."
        return response

    def _generate_priority_response(self, context_data):
        """Generate priority analysis response"""
        if not context_data or not context_data.get('latest_analysis'):
            return "🚨 I can analyze priority distributions in your tickets:\n• Critical vs. high vs. medium priorities\n• Resolution time by priority level\n• Priority trends over time\n• Client-specific priority patterns\n\nPlease upload Jira data first."

        analysis = context_data['latest_analysis']
        priorities = analysis.get('priority_distribution', {})

        if not priorities:
            return "🚨 No priority data found in your analysis. Please check your Jira data format."

        total_issues = analysis.get('issue_count', 1)
        response = "🚨 **Priority Analysis**:\n\n"

        # Sort priorities by count
        sorted_priorities = sorted(priorities.items(), key=lambda x: x[1], reverse=True)

        for priority, count in sorted_priorities:
            percentage = round(count / total_issues * 100, 1)
            response += f"• {priority}: {count} tickets ({percentage}%)\n"

        # Priority insights
        high_priority_count = priorities.get('Critical', 0) + priorities.get('Blocker', 0) + priorities.get('High', 0)
        high_priority_pct = round(high_priority_count / total_issues * 100, 1)

        response += f"\n📊 **High Priority Issues**: {high_priority_count} ({high_priority_pct}%)\n"

        if high_priority_pct > 30:
            response += "\n⚠️ **Alert**: High percentage of critical/high priority issues. Consider:\n• Reviewing triage processes\n• Identifying root causes\n• Improving preventive measures"
        elif high_priority_pct < 10:
            response += "\n✅ **Good**: Low percentage of high-priority issues indicates stable operations."

        return response

    def _generate_sentiment_response(self, context_data):
        """Generate sentiment analysis response"""
        if not context_data or not context_data.get('sentiment'):
            return "😊 I can analyze sentiment in your Jira tickets:\n• Overall satisfaction levels\n• Positive vs negative feedback\n• Client-specific sentiment\n• Sentiment trends over time\n\nPlease upload Jira data with text content for sentiment analysis."

        sentiment = context_data['sentiment']
        distribution = sentiment.get('distribution', {})

        response = "😊 **Sentiment Analysis**:\n\n"

        # Sentiment breakdown
        total_tickets = sentiment.get('total_tickets', 0)
        if total_tickets > 0:
            for sentiment_type, count in distribution.items():
                percentage = round(count / total_tickets * 100, 1)
                emoji = {"positive": "😊", "neutral": "😐", "negative_low": "😕", "negative_medium": "😞", "negative_high": "😡"}.get(sentiment_type, "📊")
                response += f"{emoji} {sentiment_type.replace('_', ' ').title()}: {count} ({percentage}%)\n"

        # Overall assessment
        negative_pct = sentiment.get('negative_percentage', 0)
        response += f"\n📊 **Overall Negative Sentiment**: {negative_pct}%\n"

        if negative_pct > 60:
            response += "\n🚨 **Concerning**: High negative sentiment requires immediate attention:\n• Review client communication\n• Identify common complaint themes\n• Implement improvement measures"
        elif negative_pct > 40:
            response += "\n⚠️ **Mixed**: Moderate negative sentiment suggests room for improvement:\n• Focus on client experience\n• Address recurring issues\n• Enhance support processes"
        else:
            response += "\n✅ **Positive**: Generally good sentiment levels. Continue current practices."

        return response

    def _generate_recommendation_response(self, context_data):
        """Generate actionable recommendations"""
        if not context_data or not context_data.get('latest_analysis'):
            return "💡 I can provide recommendations for:\n• Improving ticket resolution times\n• Optimizing priority management\n• Enhancing client satisfaction\n• Streamlining workflow processes\n• Identifying bottlenecks\n\nPlease upload Jira data first to get specific recommendations."

        analysis = context_data['latest_analysis']
        summary = context_data.get('data_summary', {})

        response = "💡 **Actionable Recommendations**:\n\n"

        # Existing insights from analysis
        if context_data.get('actionable_insights'):
            response += "🎯 **From Your Data Analysis**:\n"
            for insight in context_data['actionable_insights'][:3]:
                response += f"• {insight}\n"
            response += "\n"

        # Priority-based recommendations
        priorities = analysis.get('priority_distribution', {})
        if priorities:
            high_priority = priorities.get('Critical', 0) + priorities.get('Blocker', 0)
            total_issues = analysis.get('issue_count', 1)
            if high_priority / total_issues > 0.2:
                response += "🚨 **Priority Management**:\n• Implement stricter triage processes\n• Review escalation procedures\n• Focus on preventive measures\n\n"

        # Sentiment-based recommendations
        if context_data.get('sentiment'):
            negative_pct = context_data['sentiment'].get('negative_percentage', 0)
            if negative_pct > 40:
                response += "😊 **Client Experience**:\n• Improve communication clarity\n• Reduce resolution times\n• Implement proactive updates\n\n"

        # Client-based recommendations
        if context_data.get('client_metrics'):
            response += "👥 **Client Management**:\n• Focus on top clients with high ticket volumes\n• Implement client-specific SLAs\n• Regular satisfaction surveys\n\n"

        response += "🔄 **Next Steps**: Ask for specific recommendations in any area, or request a detailed improvement plan."
        return response

    def _generate_summary_response(self, context_data):
        """Generate comprehensive summary response"""
        if not context_data or not context_data.get('latest_analysis'):
            return "📋 Please upload and analyze Jira data first to get a comprehensive summary of your ticket metrics, client performance, and actionable insights."

        analysis = context_data['latest_analysis']
        summary = context_data.get('data_summary', {})

        response = f"📋 **Executive Summary - {analysis.get('file_name', 'Jira Analysis')}**\n\n"

        # Key metrics
        response += "📊 **Key Metrics**:\n"
        response += f"• Total Issues: {analysis.get('issue_count', 0)}\n"
        response += f"• Analysis Date: {analysis.get('analysis_date', 'N/A')}\n"
        response += f"• Clients Analyzed: {summary.get('total_clients', 0)}\n"
        response += f"• Top Priority: {summary.get('top_priority', 'N/A')}\n"
        response += f"• Top Ticket Type: {summary.get('top_ticket_type', 'N/A')}\n\n"

        # Sentiment overview
        if summary.get('sentiment_summary'):
            response += f"😊 **Sentiment**: {summary.get('sentiment_summary')}\n\n"

        # Top clients
        if summary.get('top_clients'):
            response += "👥 **Top Clients**:\n"
            for client in summary['top_clients'][:3]:
                response += f"• {client['name']}: {client['tickets']} tickets\n"
            response += "\n"

        # Trends (if available)
        if context_data.get('comparative_data'):
            comp = context_data['comparative_data']
            change = comp.get('issue_count_change', 0)
            if change != 0:
                direction = "increased" if change > 0 else "decreased"
                response += f"📈 **Trend**: Issue volume {direction} by {abs(change)} tickets\n\n"

        response += "💡 **Ask for**: Detailed analysis, specific recommendations, or trend insights."
        return response

    def _generate_help_response(self, has_data):
        """Generate help response based on data availability"""
        if has_data:
            return "🤖 **I can help you with**:\n\n📊 **Data Analysis**: Ticket metrics, distributions, patterns\n👥 **Client Insights**: Performance, satisfaction, comparisons\n📈 **Trends**: Historical patterns, changes over time\n🚨 **Priorities**: Distribution analysis, optimization\n😊 **Sentiment**: Satisfaction levels, feedback analysis\n💡 **Recommendations**: Actionable improvement suggestions\n📋 **Summaries**: Executive reports, key insights\n\n**Try asking**: 'Analyze my data', 'Show client trends', 'What are my priorities?'"
        else:
            return "🤖 **Welcome! I'm your Jira Analysis Assistant**\n\n**To get started**:\n1. Upload Jira CSV/Excel files\n2. Wait for analysis to complete\n3. Ask me questions about your data\n\n**I can analyze**:\n📊 Ticket metrics and patterns\n👥 Client performance and satisfaction\n📈 Trends and historical data\n🚨 Priority distributions\n💡 Actionable recommendations\n\n**Example questions**: 'Analyze my latest data', 'Show client insights', 'What trends do you see?'"

    def _generate_default_response(self, context_data, user_message):
        """Generate intelligent default response"""
        if context_data and context_data.get('latest_analysis'):
            return "🤖 I understand you're asking about your Jira data. I can help with:\n\n📊 **Analysis**: Ticket metrics and patterns\n👥 **Clients**: Performance and satisfaction insights\n📈 **Trends**: Historical patterns and changes\n💡 **Recommendations**: Actionable improvements\n\nCould you be more specific about what you'd like to explore?"
        else:
            return "🤖 I'm here to help with Jira analysis! Please upload some Jira files first, then I can provide intelligent insights about your tickets, clients, and trends. What would you like to analyze?"

    def get_model_info(self):
        """Get information about the currently used model"""
        if self.current_model:
            return {
                "name": self.current_model["name"],
                "type": self.current_model["type"],
                "provider": "Hugging Face (Free)"
            }
        return {
            "name": "Fallback Response System",
            "type": "rule-based",
            "provider": "Local"
        }
